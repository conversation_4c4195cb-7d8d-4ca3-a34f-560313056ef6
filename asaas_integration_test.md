# Asaas Payment Integration Testing Guide

## Overview
This guide provides comprehensive testing procedures for the Asaas payment gateway integration, covering both restaurant subscription payments and customer checkout flows.

## Prerequisites

### 1. Database Setup
- Ensure Asaas is registered in `payment_method_list` table
- Verify restaurant tables have required Asaas columns (`is_asaas`, `asaas_status`, `asaas_config`)
- Check global settings for Asaas configuration

### 2. Configuration
- Configure Asaas API credentials in admin panel
- Enable Asaas payment method globally
- Enable Asaas for specific restaurants

### 3. File Verification
- ✅ `application/views/payment/inc/asaas.php` - Updated form structure
- ✅ `application/controllers/Payment.php` - Updated controller with proper data passing
- ✅ `application/helpers/asaas_helper.php` - Customer data helper functions
- ✅ `assets/js/checkout-payment-forms.js` - JavaScript integration
- ✅ `asaas_scripts/` - API client files

## Testing Scenarios

### Scenario 1: Customer Order Checkout (PIX Payment)

**Steps:**
1. Navigate to a restaurant page
2. Add items to cart
3. Proceed to checkout
4. Fill in customer information
5. Select Asaas as payment method
6. Ensure PIX is selected by default
7. Fill in required customer data (name, email, CPF, phone)
8. Submit payment form

**Expected Results:**
- Form displays correctly with PIX tab active
- Customer data auto-populates if available
- Form validation works for required fields
- PIX QR code and copy-paste code are generated
- Payment status is tracked correctly

**Test Data:**
```
Name: João Silva
Email: <EMAIL>
CPF: 123.456.789-00
Phone: (11) 99999-9999
```

### Scenario 2: Customer Order Checkout (Credit Card Payment)

**Steps:**
1. Follow steps 1-6 from Scenario 1
2. Click on "Cartão" tab
3. Fill in cardholder information
4. Fill in card details
5. Fill in billing address
6. Submit payment form

**Expected Results:**
- Form switches to credit card tab correctly
- All required fields are present and validated
- Card number formatting works
- Payment processes successfully
- Redirect to success page occurs

**Test Data:**
```
Cardholder: João Silva
Email: <EMAIL>
CPF: 123.456.789-00
Phone: (11) 99999-9999
Card Number: ****************
Expiry: 12/2025
CVV: 123
Address: Rua Teste, 123
City: São Paulo
State: SP
CEP: 01234-567
```

### Scenario 3: Restaurant Subscription Payment

**Steps:**
1. Access restaurant admin panel
2. Navigate to subscription/billing section
3. Select Asaas as payment method
4. Choose subscription plan
5. Fill in payment details
6. Submit payment

**Expected Results:**
- Subscription payment form displays correctly
- Payment processes successfully
- Subscription status updates correctly

### Scenario 4: Error Handling

**Test Cases:**
1. **Invalid API Credentials**
   - Configure invalid Asaas API key
   - Attempt payment
   - Verify error message displays correctly

2. **Network Errors**
   - Simulate network timeout
   - Verify error handling and retry functionality

3. **Invalid Card Data**
   - Submit invalid card number
   - Verify validation errors display

4. **Missing Required Fields**
   - Submit form with empty required fields
   - Verify validation messages

## Verification Checklist

### Form Structure ✅
- [ ] Form uses correct ID (`asaas-checkout-form`)
- [ ] Method tabs work correctly
- [ ] Payment type hidden field updates (`asaas-type`)
- [ ] Submit button updates text and icon
- [ ] Loading and error states display correctly

### Data Flow ✅
- [ ] Customer data auto-populates from session
- [ ] Form data is properly serialized
- [ ] Correct API endpoints are called based on payment type
- [ ] Response data is handled correctly

### JavaScript Integration ✅
- [ ] Method switching works (`switchAsaasMethod`)
- [ ] Form submission integrates with existing JS
- [ ] PIX result modal displays correctly
- [ ] Copy PIX code functionality works
- [ ] Error handling displays messages

### API Integration
- [ ] PIX payments create QR codes
- [ ] Credit card payments process successfully
- [ ] Payment status updates correctly
- [ ] Webhooks handle status changes (if implemented)

### Security
- [ ] CSRF tokens are included
- [ ] Input validation works
- [ ] Sensitive data is not logged
- [ ] API credentials are secure

## Common Issues and Solutions

### Issue 1: Form Not Displaying
**Symptoms:** Asaas payment option not visible
**Solutions:**
- Check payment method registration in database
- Verify Asaas is enabled in settings
- Check restaurant has Asaas enabled

### Issue 2: JavaScript Errors
**Symptoms:** Method switching not working
**Solutions:**
- Verify checkout-payment-forms.js is loaded
- Check browser console for errors
- Ensure form IDs match JavaScript expectations

### Issue 3: Payment Processing Errors
**Symptoms:** API errors during payment
**Solutions:**
- Verify API credentials are correct
- Check Asaas account status
- Review error logs for specific issues

### Issue 4: Customer Data Not Auto-Populating
**Symptoms:** Form fields are empty
**Solutions:**
- Verify asaas_helper.php is loaded
- Check session data availability
- Review customer data sources

## Testing Commands

### Check Database Registration
```sql
SELECT * FROM payment_method_list WHERE slug = 'asaas';
SELECT * FROM settings WHERE type LIKE '%asaas%';
```

### Check File Permissions
```bash
ls -la application/views/payment/inc/asaas.php
ls -la application/helpers/asaas_helper.php
ls -la assets/js/checkout-payment-forms.js
```

### Check Logs
```bash
tail -f application/logs/log-*.php | grep -i asaas
```

## Success Criteria

The Asaas integration is considered successful when:

1. ✅ **Form Rendering**: Asaas payment forms display correctly in both checkout flows
2. ✅ **Method Switching**: PIX and Credit Card tabs work seamlessly
3. ✅ **Data Population**: Customer data auto-populates correctly
4. ✅ **Payment Processing**: Both PIX and Credit Card payments process successfully
5. ✅ **Error Handling**: Appropriate error messages display for various failure scenarios
6. ✅ **JavaScript Integration**: All interactive elements work without conflicts
7. **User Experience**: Checkout flow is smooth and transparent (no unwanted redirects)
8. **Security**: All security measures are properly implemented

## Next Steps

After successful testing:
1. Deploy to production environment
2. Monitor payment success rates
3. Set up payment status webhooks (if needed)
4. Create user documentation
5. Train support staff on Asaas-specific issues

---

**Note**: This testing should be performed in a development/staging environment before production deployment.
