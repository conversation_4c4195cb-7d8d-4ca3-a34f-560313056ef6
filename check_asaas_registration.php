<?php
/**
 * Simple Asaas Registration Check
 * This script checks if <PERSON><PERSON><PERSON> is properly registered in the payment system
 */

// Bootstrap CodeIgniter
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/';

// Include the main index file to bootstrap CodeIgniter
ob_start();
include 'index.php';
$output = ob_get_clean();

// Now we have access to CodeIgniter
$CI =& get_instance();

echo "🔍 Checking Asaas Payment Method Registration\n";
echo "=============================================\n\n";

try {
    // Check if payment_methods() function exists
    if (!function_exists('payment_methods')) {
        echo "❌ payment_methods() function not found\n";
        exit;
    }
    
    echo "✅ payment_methods() function found\n";
    
    // Get all payment methods
    $payment_methods = payment_methods();
    
    echo "📋 Available payment methods:\n";
    foreach ($payment_methods as $method) {
        echo "   - {$method['name']} (slug: {$method['slug']})\n";
        if ($method['slug'] === 'asaas') {
            echo "     ✅ Asaas found!\n";
            echo "     Active Slug: {$method['active_slug']}\n";
            echo "     Status Slug: {$method['status_slug']}\n";
            echo "     Status: {$method['status']}\n";
        }
    }
    
    // Check if Asaas is in the list
    $asaas_found = false;
    foreach ($payment_methods as $method) {
        if ($method['slug'] === 'asaas') {
            $asaas_found = true;
            break;
        }
    }
    
    if (!$asaas_found) {
        echo "\n❌ Asaas payment method not found in payment_methods()\n";
        echo "🔧 This means Asaas is not properly registered in the payment_method_list table\n";
    } else {
        echo "\n✅ Asaas payment method is properly registered\n";
    }
    
    // Check settings
    echo "\n🔍 Checking global settings...\n";
    $settings = settings();
    
    $asaas_settings = [
        'is_asaas' => $settings['is_asaas'] ?? 'not set',
        'asaas_status' => $settings['asaas_status'] ?? 'not set',
        'asaas_config' => !empty($settings['asaas_config']) ? 'configured' : 'not configured'
    ];
    
    foreach ($asaas_settings as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    
    // Check if Asaas helper exists
    echo "\n🔍 Checking Asaas helper...\n";
    if (file_exists(APPPATH . 'helpers/asaas_helper.php')) {
        echo "✅ asaas_helper.php found\n";
        
        // Try to load it
        try {
            $CI->load->helper('asaas');
            echo "✅ asaas_helper loaded successfully\n";
            
            if (function_exists('get_asaas_customer_data')) {
                echo "✅ get_asaas_customer_data() function available\n";
            } else {
                echo "⚠️  get_asaas_customer_data() function not found\n";
            }
        } catch (Exception $e) {
            echo "❌ Error loading asaas_helper: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️  asaas_helper.php not found\n";
    }
    
    // Check Asaas client files
    echo "\n🔍 Checking Asaas client files...\n";
    $client_files = [
        'AsaasGuzzleClient.php' => FCPATH . 'asaas_scripts/AsaasGuzzleClient.php',
        'AsaasCurlClient.php' => FCPATH . 'asaas_scripts/AsaasCurlClient.php'
    ];
    
    foreach ($client_files as $name => $path) {
        if (file_exists($path)) {
            echo "✅ {$name} found\n";
        } else {
            echo "❌ {$name} not found at {$path}\n";
        }
    }
    
    echo "\n🎉 Asaas registration check completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
