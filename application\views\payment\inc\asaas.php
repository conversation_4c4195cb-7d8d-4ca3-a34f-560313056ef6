<?php
// Variables are passed from the controller
// $shop, $asaas_config, $payment, $total_amount, $order_id, $customer_data are available
?>

<?php if (!empty($asaas_config['asaas_api_key'])): ?>
<div class="payment_content text-center <?= $pay['slug'];?>">
    <div class="card_img">
        <div class="payment_card_img">
            <img src="<?php echo base_url('assets/frontend/images/payment.png'); ?>" alt="">
        </div>
    </div>

    <div class="payment_details">
        <div class="userInfo">
            <h4><?= isset($payment['name']) ? html_escape($payment['name']) : ''; ?></h4>
            <p><?= lang('phone'); ?>: <?= isset($payment['phone']) ? html_escape($payment['phone']) : ''; ?></p>
        </div>
        <div class="">
            <h2><?= isset($total_amount) ? currency_position($total_amount, $shop['id']) : ''; ?></h2>
        </div>
        <p class="payment_text">*<?= !empty(lang('payment_by')) ? lang('payment_by') : "Payment via"?> Asaas</p>
    </div>

    <!-- Payment Method Selection -->
    <div class="payment-method-selection">
        <div class="method-tabs">
            <button type="button" class="method-tab active" data-method="pix">
                <i class="fa fa-qrcode"></i> PIX
            </button>
            <button type="button" class="method-tab" data-method="credit_card">
                <i class="fa fa-credit-card"></i> Cartão
            </button>
        </div>
    </div>

    <!-- Asaas Payment Form -->
    <form role="form" action="<?= base_url('payment/process_asaas_payment');?>" method="post" class="require-validation" id="asaasPaymentForm">
        <!-- CSRF Token -->
        <input type="hidden" name="<?= $this->security->get_csrf_token_name(); ?>" value="<?= $this->security->get_csrf_hash(); ?>">
        <input type="hidden" name="order_id" value="<?= htmlspecialchars($order_id); ?>">
        <input type="hidden" name="amount" value="<?= number_format((float)$total_amount, 2, '.', '');?>">
        <input type="hidden" name="username" value="<?= isset($slug) ? $slug : "";?>">
        <input type="hidden" name="shop_id" value="<?= isset($shop['id']) ? $shop['id'] : 0;?>">
        <input type="hidden" name="payment_type" id="asaas-payment-type" value="PIX">

        <div class="card-body">
            <div class='col-md-12 error form-group payment-errors'></div>

            <!-- PIX Form Fields -->
            <div id="pix-fields" class="payment-method-fields active">
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'><?= lang('name'); ?></label>
                        <input class='form-control' name="customer_name" type='text' placeholder="Nome completo"
                               value="<?= htmlspecialchars($customer_data['name'] ?? $payment['name'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>E-mail</label>
                        <input class='form-control' name="customer_email" type='email' placeholder="<EMAIL>"
                               value="<?= htmlspecialchars($customer_data['email'] ?? $payment['email'] ?? ''); ?>" required>
                    </div>
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>CPF</label>
                        <input class='form-control' name="customer_cpf" type='text' placeholder="000.000.000-00"
                               value="<?= htmlspecialchars($customer_data['cpf'] ?? ''); ?>" required maxlength="14">
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'><?= lang('phone'); ?></label>
                        <input class='form-control' name="customer_phone" type='tel' placeholder="(11) 99999-9999"
                               value="<?= htmlspecialchars($customer_data['phone'] ?? $payment['phone'] ?? ''); ?>" required>
                    </div>
                </div>
            </div>

            <!-- Credit Card Form Fields -->
            <div id="credit-card-fields" class="payment-method-fields">
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'>Nome no Cartão</label>
                        <input class='form-control' name="holderName" type='text' placeholder="Nome impresso no cartão"
                               value="<?= htmlspecialchars($customer_data['name'] ?? $payment['name'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>E-mail</label>
                        <input class='form-control' name="email" type='email' placeholder="<EMAIL>"
                               value="<?= htmlspecialchars($customer_data['email'] ?? $payment['email'] ?? ''); ?>" required>
                    </div>
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>CPF do Titular</label>
                        <input class='form-control' name="holderCpf" type='text' placeholder="000.000.000-00"
                               value="<?= htmlspecialchars($customer_data['cpf'] ?? ''); ?>" required maxlength="14">
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'>Telefone</label>
                        <input class='form-control' name="holderPhone" type='tel' placeholder="(11) 99999-9999"
                               value="<?= htmlspecialchars($customer_data['phone'] ?? $payment['phone'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'>Número do Cartão</label>
                        <input class='form-control card-number' name="cardNumber" type='text' placeholder="**** **** **** ****" required maxlength="19">
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-4 form-group required'>
                        <label class='control-label'>Mês</label>
                        <input class='form-control' name="expiryMonth" placeholder='MM' size='2' type='text' maxlength="2" required>
                    </div>
                    <div class='col-md-4 form-group required'>
                        <label class='control-label'>Ano</label>
                        <input class='form-control' name="expiryYear" placeholder='YYYY' size='4' type='text' maxlength="4" required>
                    </div>
                    <div class='col-md-4 form-group required'>
                        <label class='control-label'>CVV</label>
                        <input class='form-control' name="ccv" placeholder='***' size='4' type='text' maxlength="4" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>CEP</label>
                        <input class='form-control' name="postal_code" type='text' placeholder="00000-000"
                               value="<?= htmlspecialchars($customer_data['postal_code'] ?? ''); ?>" required maxlength="9">
                    </div>
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>Número</label>
                        <input class='form-control' name="address_number" type='text' placeholder="123"
                               value="<?= htmlspecialchars($customer_data['address_number'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-12 form-group required'>
                        <label class='control-label'>Endereço</label>
                        <input class='form-control' name="address" type='text' placeholder="Rua, Avenida, etc."
                               value="<?= htmlspecialchars($customer_data['address'] ?? ''); ?>" required>
                    </div>
                </div>
                <div class="row">
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>Cidade</label>
                        <input class='form-control' name="city" type='text' placeholder="Cidade"
                               value="<?= htmlspecialchars($customer_data['city'] ?? ''); ?>" required>
                    </div>
                    <div class='col-md-6 form-group required'>
                        <label class='control-label'>Estado (UF)</label>
                        <input class='form-control' name="state" type='text' placeholder="UF"
                               value="<?= htmlspecialchars($customer_data['state'] ?? ''); ?>" required maxlength="2">
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <?php if(is_demo() == 0): ?>
                <button class="btn btn-primary btn-lg btn-block" id="asaasPayBtn" type="submit">
                    <span id="asaas-btn-text"><?= !empty(lang('pay_now')) ? lang('pay_now') : "Pay Now "?> <?= currency_position($total_amount, $shop['id']); ?></span>
                </button>
            <?php endif;?>
        </div>
    </form>
</div>

<!-- PIX Result Modal -->
<div id="asaas-pix-result" style="display: none;" class="pix-result-modal">
    <div class="modal-content">
        <h4><i class="fas fa-check-circle text-success"></i> PIX Gerado com Sucesso!</h4>

        <div class="qr-code-container">
            <img id="asaas-pix-qrcode" src="" alt="QR Code PIX" class="img-fluid">
        </div>

        <div class="pix-code-container">
            <label>PIX Copia e Cola:</label>
            <textarea id="asaas-pix-code" class="pix-code" rows="3" readonly></textarea>
            <button type="button" class="btn btn-info btn-sm mt-2" onclick="copyAsaasPixCode()">
                <i class="fas fa-copy"></i> Copiar Código PIX
            </button>
        </div>

        <div class="instructions">
            <h5><i class="fas fa-info-circle"></i> Como pagar:</h5>
            <ol>
                <li>Abra o app do seu banco</li>
                <li>Escaneie o QR Code ou cole o código PIX</li>
                <li>Confirme o pagamento</li>
                <li>Aguarde a confirmação automática</li>
            </ol>
        </div>
    </div>
</div>

<?php else: ?>
    <div class="payment_content text-center">
        <h4><?= !empty(lang('credentials_not_found')) ? lang('credentials_not_found') : "Credentials not found" ;?></h4>
    </div>
<?php endif;?>

<style>
/* Asaas Payment Method Styles */
.payment-method-selection {
    margin-bottom: 20px;
}

.method-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.method-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 600;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.method-tab:hover {
    color: #007bff;
}

.method-tab.active {
    color: #007bff;
    border-color: #007bff;
}

.payment-method-fields {
    display: none;
}

.payment-method-fields.active {
    display: block;
}

.pix-result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.pix-result-modal .modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.qr-code-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px auto;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    max-width: 300px;
}

.pix-code-container {
    background: #2c3e50;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
}

.pix-code {
    background: transparent;
    border: none;
    color: #007bff;
    font-family: monospace;
    font-size: 12px;
    resize: none;
    width: 100%;
}

.instructions {
    background: #e3f2fd;
    color: #2c3e50;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
}

.instructions h5 {
    margin-bottom: 15px;
    font-weight: 600;
    color: #007bff;
    text-align: center;
}

.instructions ol {
    margin-bottom: 0;
    padding-left: 20px;
}

.instructions li {
    margin-bottom: 8px;
}
</style>

<script>
// Get base URL from the page
const base_url = $('#base_url').attr('href') || '<?= base_url(); ?>';

$(document).ready(function() {
    // Initialize method switching
    $('.method-tab').on('click', function() {
        const method = $(this).data('method');
        switchAsaasMethod(method);
    });

    // Initialize with PIX as default
    switchAsaasMethod('pix');

    // Form submission
    $('#asaasPaymentForm').on('submit', function(e) {
        e.preventDefault();

        const paymentType = $('#asaas-payment-type').val();
        const $btn = $('#asaasPayBtn');
        const originalText = $btn.html();

        // Show loading state
        $btn.html('<i class="fa fa-spinner fa-spin"></i> Processando...').prop('disabled', true);
        $('.payment-errors').empty();

        // Determine the correct endpoint based on payment type
        let apiUrl = $(this).attr('action');
        if (paymentType === 'PIX') {
            apiUrl = base_url + 'payment/process_asaas_pix';
        } else {
            apiUrl = base_url + 'payment/process_asaas_credit_card';
        }

        // Prepare form data
        const formData = $(this).serialize();

        $.ajax({
            url: apiUrl,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    if (paymentType === 'PIX' && response.qrcode) {
                        // Show PIX result
                        showPixResult(response);
                    } else if (response.redirect_url) {
                        // Redirect for credit card success
                        window.location.href = response.redirect_url;
                    }
                } else {
                    // Show error
                    $('.payment-errors').html(`
                        <div class="alert alert-danger">
                            <strong>Erro:</strong> ${response.message || 'Erro ao processar pagamento'}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                let errorMsg = 'Erro ao processar pagamento';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                $('.payment-errors').html(`
                    <div class="alert alert-danger">
                        <strong>Erro:</strong> ${errorMsg}
                    </div>
                `);
            },
            complete: function() {
                // Restore button
                $btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Copy PIX code function
    window.copyAsaasPixCode = function() {
        const pixCode = document.getElementById('asaas-pix-code');
        pixCode.select();
        pixCode.setSelectionRange(0, 99999);

        try {
            document.execCommand('copy');
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Copiado!';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-info');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.add('btn-info');
                btn.classList.remove('btn-success');
            }, 2000);
        } catch (err) {
            alert('Código PIX copiado!');
        }
    };
});

function switchAsaasMethod(method) {
    // Update tab states
    $('.method-tab').removeClass('active');
    $(`.method-tab[data-method="${method}"]`).addClass('active');

    // Update form fields visibility
    $('.payment-method-fields').removeClass('active');
    $(`#${method.replace('_', '-')}-fields`).addClass('active');

    // Update payment type
    $('#asaas-payment-type').val(method.toUpperCase());

    // Update button text
    const btnText = method === 'pix' ? 'Pagar com PIX' : 'Pagar com Cartão';
    $('#asaas-btn-text').text(btnText + ' <?= currency_position($total_amount, $shop['id']); ?>');
}

function showPixResult(data) {
    $('#asaas-pix-qrcode').attr('src', data.qrcode);
    $('#asaas-pix-code').val(data.qrcode_copy_paste);
    $('#asaas-pix-result').fadeIn();
}
</script>