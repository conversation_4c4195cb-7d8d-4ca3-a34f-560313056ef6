<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

use MyFatoorah\Library\API\Payment\MyFatoorahPaymentStatus;

class Payment extends MY_Controller
{
    protected $client;
    function  __construct()
    {
        parent::__construct();
        $this->load->library('paypal_lib');
        $this->config->load('stripe_config');
        $this->load->model('payment_m');
        // is_login();
        try {
            // Tentativa de inicializar o GuzzleHttp\Client, mas com tratamento de erro
            if (class_exists('GuzzleHttp\Client')) {
        $this->client = new Client();
            } else {
                // Se não estiver disponível, criamos um cliente vazio
                // O tratamento real será feito na classe AsaasGuzzleClient
                $this->client = null;
                error_log('GuzzleHttp\Client não encontrado no construct do Payment controller');
            }
        } catch (Exception $e) {
            $this->client = null;
            error_log('Erro ao inicializar GuzzleHttp\Client: ' . $e->getMessage());
        }
    }

    public function total_amount()
    {
        $amount = temp('total_amount');
        return $amount;
    }

    /**
     * Método privado para garantir que temos valor válido para o pedido
     * 
     * @param string $order_id ID do pedido
     * @return float Valor do pedido ou 0 se não for possível obter
     */
    private function ensure_valid_order_amount($order_id) 
    {
        if (empty($order_id)) {
            error_log("[Payment] ensure_valid_order_amount: ID de pedido vazio");
            return 0;
        }
        
        // Verificar se o pedido existe no banco de dados
        $this->load->model('common_m');
        $order_info = $this->common_m->get_by_id($order_id, 'order_user_list');
        
        if (empty($order_info)) {
            error_log("[Payment] ensure_valid_order_amount: Pedido não encontrado: " . $order_id);
            return 0;
        }
        
        // Verificar se o pedido tem um valor válido
        if (!isset($order_info['total']) || floatval($order_info['total']) <= 0) {
            error_log("[Payment] ensure_valid_order_amount: Pedido com valor inválido: " . $order_id);
            
            // Tentar calcular o valor total baseado nos itens do pedido
            $order_items = $this->common_m->get_all_order_item_by_order_id($order_id);
            
            if (!empty($order_items)) {
                $calculated_total = 0;
                foreach ($order_items as $item) {
                    $item_total = floatval($item['item_price']) * intval($item['qty']);
                    $calculated_total += $item_total;
                }
                
                if ($calculated_total > 0) {
                    error_log("[Payment] ensure_valid_order_amount: Valor calculado dos itens: " . $calculated_total);
                    
                    // Atualizar o valor do pedido no banco de dados
                    $this->common_m->update(['total' => $calculated_total], $order_id, 'order_user_list');
                    
                    return $calculated_total;
                }
            }
            
            return 0;
        }
        
        return floatval($order_info['total']);
    }

    public function index($slug = null, $account_slug = null)
    {
        $data = array();
        $settings = settings();
        $data['settings'] = $settings;
        
        // Se os parâmetros não foram fornecidos, tentar obter da URL ou GET
        if (empty($slug)) {
            $slug = $this->input->get('method', true) ?: $this->uri->segment(2);
        }
        if (empty($account_slug)) {
            $account_slug = $this->input->get('account', true) ?: $this->uri->segment(3);
        }
        
        // Se ainda não temos slug, usar padrão
        if (empty($slug)) {
            $slug = 'asaas'; // Método padrão
        }
        
        $data['slug'] = $slug;
        $data['account_slug'] = $account_slug;
        $data['id'] = $this->input->get('pid', true);
        $data['order_id'] = $this->input->get('order_id', true);
        $data['amount'] = $this->input->get('amount', true);
        
        // Log para depuração
        error_log("[Payment Index] Slug: {$slug}, Account: {$account_slug}, ID: {$data['id']}, Order ID: {$data['order_id']}, Amount: {$data['amount']}");

        // Para métodos de pagamento específicos, verificar se temos um order_id válido
        if (in_array($slug, ['asaas', 'stripe', 'razorpay']) && empty($data['order_id'])) {
            error_log("[Payment Index] Ordem não definida para método {$slug}, mas continuando");
            // Não redirecionar mais, apenas log
        }

        // Recuperar dados do pedido se o valor não foi informado
        if (!empty($data['order_id']) && (empty($data['amount']) || floatval($data['amount']) <= 0)) {
            error_log("[Payment Index] Valor não definido ou inválido, tentando recuperar do banco de dados");
            
            // Usar nosso método aprimorado para garantir um valor válido
            $amount = $this->ensure_valid_order_amount($data['order_id']);
            
            if ($amount > 0) {
                $data['amount'] = $amount;
                error_log("[Payment Index] Valor recuperado com sucesso: {$data['amount']}");
            } else {
                error_log("[Payment Index] Não foi possível recuperar o valor do pedido");
            }
        }

        // Carregar o método de pagamento selecionado
        if($slug == 'offline'):
            $data['page_title'] = "Pagamento Offline";
            $data['page'] = 'payment/offline_payment';
            $this->load->view('payment_index', $data);
        elseif($slug == 'stripe'):
            $data['page_title'] = "Pagamento via Stripe";
            $data['page'] = 'payment/stripe_payment';
            $this->load->view('payment_index', $data);
        elseif($slug == 'razorpay'):
            $data['page_title'] = "Pagamento via Razorpay";
            $data['page'] = 'payment/razorpay_payment';
            $this->load->view('payment_index', $data);
        elseif($slug == 'stripe-fpx'):
            $data['page_title'] = "Pagamento via Stripe FPX";
            $data['page'] = 'payment/stripe_fpx';
            $this->load->view('payment_index', $data);
        elseif($slug == 'paytm'):
            $data['page_title'] = "Pagamento via Paytm";
            $data['payment'] = $this->payment_m->paytm_init($slug,$account_slug);
            $data['page'] = 'payment/paytm';
            $this->load->view('payment_index', $data);
        elseif($slug == 'flutterwave'):
            $data['page_title'] = "Pagamento via Flutterwave";
            $data['page'] = 'payment/flutterwave';
            $this->load->view('payment_index', $data);
        elseif($slug == 'paystack'):
            $data['page_title'] = "Pagamento via Paystack";
            $data['page'] = 'payment/paystack';
            $this->load->view('payment_index', $data);
        elseif($slug == 'mercado'):
            $data['page_title'] = "Pagamento via Mercado Pago";
            $data['page'] = 'payment/mercado';
            $this->load->view('payment_index', $data);
        elseif($slug == 'mercado_pix'):
            $data['page_title'] = "Pagamento via PIX - Mercado Pago";

            // Get restaurant configuration for Mercado Pago PIX
            $payment_data = $this->session->userdata('payment');
            $shop_id = isset($payment_data['shop_id']) ? $payment_data['shop_id'] : 0;
            $restaurant = $this->common_m->get_restaurant_info_by_id($shop_id);

            // Pass restaurant configuration to the view
            $data['restaurant'] = $restaurant;
            $data['mercado_config'] = [];
            if (!empty($restaurant['mercado_config']) && isJson($restaurant['mercado_config'])) {
                $data['mercado_config'] = json_decode($restaurant['mercado_config'], true);
            }

            $data['page'] = 'payment/mercado_pix';
            $this->load->view('payment_index', $data);
        elseif($slug == 'pagadito'):
            $data['page_title'] = "Pagamento via Pagadito";
            $data['payment'] = $this->payment_m->pagadito_verify($settings['pagadito_config']);
            $data['page'] = 'payment/pagadito';
            $this->load->view('payment_index', $data);
        elseif($slug == 'netseasy'):
            $data['page_title'] = "Pagamento via Nets Easy";
            $data['payment'] = $this->payment_m->netseasy_init($slug,$account_slug);
            $data['page'] = 'payment/netseasy';
            $this->load->view('payment_index', $data);
        elseif($slug == 'midtrans'):
            $data['page_title'] = "Pagamento via Midtrans";
            $data['payment'] = $this->payment_m->midtrans_init($slug,$account_slug);
            $data['page'] = 'payment/midtrans';
            $this->load->view('payment_index', $data);
        elseif($slug == 'cashfree'):
            $data['page_title'] = "Pagamento via Cashfree";
            $data['payment'] = $this->payment_m->cashfree_init($slug,$account_slug);
            $data['page'] = 'payment/cashfree';
            $this->load->view('payment_index', $data);
        elseif($slug == 'myfatoorah'):
            $data['page_title'] = "Pagamento via My Fatoorah";
            $data['payment'] = $this->payment_m->myfatoorah_init($slug,$account_slug);
            $data['page'] = 'payment/myfatoorah';
            $this->load->view('payment_index', $data);
        elseif($slug == 'iyzico'):
            $data['page_title'] = "Pagamento via Iyzico";
            $data['payment'] = $this->payment_m->iyzico_init($slug,$account_slug);
            $data['page'] = 'payment/iyzico';
            $this->load->view('payment_index', $data);
        elseif($slug == 'asaas'):
            $data['page_title'] = "Pagamento via Asaas";

            // Get Asaas configuration and customer data
            $shop = $this->admin_m->get_restaurant_info_slug($account_slug);
            $data['shop'] = $shop;
            $data['asaas_config'] = json_decode($shop['asaas_config'] ?? '{}', true);

            // Get payment data
            $payment_data = auth('payment');
            $data['payment'] = $payment_data;
            $data['total_amount'] = price_details($payment_data['shop_id'], $payment_data)->grand_total;
            $data['order_id'] = $payment_data['uid'] ?? '';

            // Load customer data helper if available
            if (function_exists('get_asaas_customer_data')) {
                $data['customer_data'] = get_asaas_customer_data($shop['id']);
            } else {
                $data['customer_data'] = [];
            }

            $data['page'] = 'payment/inc/asaas';
            $this->load->view('payment_index', $data);
        endif;
    }



    public function iyzico($slug = null, $account_slug = null)
    {

        $this->security->csrf_verify = false;
        $postData = $this->input->get();
        if (isset($postData['token']) && empty($postData['token'])) {
            $this->session->set_flashdata('error', 'Token not found');
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }

        if (empty($slug) && empty($account_slug)) {
            $this->session->set_flashdata('error', 'User not found');
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }

        $settings = settings();

        $iyzico = isJson($settings['iyzico_config']) ? json_decode($settings['iyzico_config']) : '';
        $baseUrl = isset($iyzico->is_iyzico_live) && $iyzico->is_iyzico_live == 1 ? 'https://api.iyzipay.com' : 'https://sandbox-api.iyzipay.com';



        $checkoutFormToken = $postData['token'];



        $options = new \Iyzipay\Options();
        $options->setApiKey(isset($iyzico->iyzico_api_key) && !empty($iyzico->iyzico_api_key) ? $iyzico->iyzico_api_key : '');  // Use your sandbox key
        $options->setSecretKey(isset($iyzico->iyzico_secret_key) && !empty($iyzico->iyzico_secret_key) ? $iyzico->iyzico_secret_key : '');  // Use your sandbox secret
        $options->setBaseUrl($baseUrl);  // Sandbox URL for testing


        $request = new \Iyzipay\Request\RetrieveCheckoutFormRequest();
        $request->setLocale(\Iyzipay\Model\Locale::TR);  
        $request->setConversationId(uniqid()); 
        $request->setToken($checkoutFormToken);  

        // Retrieve checkout form result
        $checkoutForm = \Iyzipay\Model\CheckoutForm::retrieve($request, $options);


        if ($checkoutForm->getStatus() === "success") {
            $paidAmount = $checkoutForm->getPaidPrice();
            $currency = $checkoutForm->getCurrency();
            $transactionId = $checkoutForm->getPaymentId();
            $data = [
                'amount' => $paidAmount,
                'currency' => $currency,
                'status' => 'success',
                'txn_id' => $transactionId,
                'payment_type' => 'izyico',
                'all_info' => json_encode($checkoutForm->getRawResult()),
            ];
            $this->send_success($slug, $account_slug, $data);
        } else {
            echo $checkoutForm->getErrorMessage();
            $this->send_failed($slug, $account_slug);
        }
    }


    public function iyzico_callback()
    {

        $transaction_id = $this->input->get('transaction_id');
        $slug = $this->input->get('slug');
        $account_slug = $this->input->get('account_slug');


        $temp_file = APPPATH . 'temp/' . $transaction_id . '.tmp';

        if (file_exists($temp_file)) {
            $encoded_session_data = file_get_contents($temp_file);
            $session_data = json_decode(base64_decode($encoded_session_data), true);

            if (isset($session_data['csrf_token'])) {
                $this->security->csrf_set_cookie();
                $this->security->csrf_hash = $session_data['csrf_token'];
                unset($session_data['csrf_token']);
            }

            $this->session->set_userdata($session_data);

            unlink($temp_file);
        }



        $this->security->csrf_verify = false;

        $postData = $this->input->post();


        if (!isset($postData['token']) || empty($postData['token'])) {
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }

        if (empty($slug) || empty($account_slug)) {
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }


        redirect(base_url("payment/iyzico/{$slug}/{$account_slug}?token=" . $postData['token']));
        exit();
    }




    private function log_callback_data($context, $data = null)
    {
        $logFile = APPPATH . 'logs/iyzico_callback.log'; 

 
        $logMessage = date('Y-m-d H:i:s') . " - $context\n";
        if ($data !== null) {
            $logMessage .= print_r($data, true) . "\n";
        }

        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }




    public function myfatoorah()
    {
        $get = $this->input->get();


        $payment_id = $get['paymentId'];
        $order_id = $get['Id'];
        $slug = $get['slug'];
        $account_slug = $get['account_slug'];

        if (empty($payment_id) && empty($slug)) {
            $this->session->set_flashdata('error', __('error_msg'));
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }

        $keyId   = $payment_id;
        $KeyType = 'paymentId';

        $settings = settings();
        $u_info = get_all_user_info_slug($slug);
        $myfatoorah = isJson($settings['myfatoorah_config']) ? json_decode($settings['myfatoorah_config']) : '';

        $mfConfig = [
            'apiKey'      => isset($myfatoorah->myfatoorah_api_key) && !empty($myfatoorah->myfatoorah_api_key) ? $myfatoorah->myfatoorah_api_key : '',
            'vcCode' => isset($myfatoorah->vccode) && !empty($myfatoorah->vccode) ? $myfatoorah->vccode : 'KWT',
            'isTest'      => isset($myfatoorah->is_myfatoorah_live) && $myfatoorah->is_myfatoorah_live == 1 ? false : true,
        ];

        try {
            $mfObj = new MyFatoorahPaymentStatus($mfConfig);
            $result  = $mfObj->getPaymentStatus($keyId, $KeyType);
            if (isset($result->InvoiceStatus, $result->focusTransaction->PaymentId) && strtolower($result->InvoiceStatus) == "paid") {
                $data = array(
                    'amount' => $result->focusTransaction->PaidCurrencyValue,
                    'currency' => $result->focusTransaction->Currency,
                    'status' => 'success',
                    'txn_id' => $result->focusTransaction->TrackId,
                    'payment_type' => 'myfatoorah',
                    'all_info' => json_encode($result),
                );
                $this->send_success($slug, $account_slug, $data);
            } else {
                $this->send_failed($slug, $account_slug);
            }
        } catch (Exception $ex) {
            echo $ex->getMessage();
            die;
        }
    }


    public function cashfree()
    {
        $orderId = $this->input->get('order_id', true);
        $slug = $this->input->get('slug', true);
        $account_slug = $this->input->get('account_slug', true);

        if (empty($orderId) && empty($slug)) {
            $this->session->set_flashdata('error', __('error_msg'));
            redirect(base_url("admin/auth/subscriptions"));
            exit();
        }

        $settings = settings();
        $u_info = get_all_user_info_slug($slug);
        $cashfree = isJson($settings['cashfree_config']) ? json_decode($settings['cashfree_config']) : '';

        // Replace with your actual Cashfree API credentials
        $apiKey = isset($cashfree->cashfree_app_id) ? $cashfree->cashfree_app_id : '';
        $apiSecret = isset($cashfree->cashfree_secret_key) ? $cashfree->cashfree_secret_key : '';

        // Replace with the actual order ID
        $orderId = $_GET['order_id'];
        if (isset($cashfree->is_cashfree_live) && $cashfree->is_cashfree_live == 1):
            $url = "https://cashfree.com/pg/orders/{$orderId}";
        else:
            $url = "https://sandbox.cashfree.com/pg/orders/{$orderId}";
        endif;


        try {
            // Make a GET request to the URL
            $response = $this->client->request('GET', $url, [
                'headers' => [
                    'Accept' => 'application/json',
                    'x-client-id' => $apiKey,
                    'x-client-secret' => $apiSecret,
                    'x-api-version' => '2022-09-01'  // Current API version as of my last update
                ]
            ]);

            // Get the response body as a string
            $body = $response->getBody()->getContents();

            // Parse the JSON response
            $result = json_decode($body);

            if (isset($result->order_status) && strtolower($result->order_status) == "paid") {
                $data = array(
                    'amount' => $result->order_amount,
                    'currency' => $result->order_currency,
                    'status' => 'success',
                    'txn_id' => $result->order_id,
                    'payment_type' => 'cashfree',
                    'all_info' => json_encode($result),
                );
                $this->send_success($slug, $account_slug, $data);
            } else {
                $this->send_failed($slug, $account_slug);
            }
        } catch (\Exception $e) {
            echo "An error occurred: " . $e->getMessage();
        }
    }

    public function midtrans()
    {
        $order_id = $this->input->get('order_id');
        $slug = $this->input->get('slug');
        $account_slug = $this->input->get('account_slug');

        $settings = settings();
        $u_info = get_all_user_info_slug($slug);
        $midtrans = isJson($settings['midtrans_config']) ? json_decode($settings['midtrans_config']) : '';


        \Midtrans\Config::$serverKey = isset($midtrans->server_key) ? $midtrans->server_key : '';
        \Midtrans\Config::$isProduction = isset($midtrans->is_midtrans_live) && $midtrans->is_midtrans_live == 1 ? true : false; // Set to true for production
        \Midtrans\Config::$isSanitized = true;
        \Midtrans\Config::$is3ds = true;
        try {

            $status = \Midtrans\Transaction::status($order_id);
            $status = (object) $status;



            if ($status->transaction_status == 'settlement' || $status->transaction_status == 'capture') {
                $data = array(
                    'amount' => $status->gross_amount,
                    'currency' => $status->currency,
                    'status' => 'success',
                    'txn_id' => $status->transaction_id,
                    'payment_type' => 'midtrans',
                    'all_info' => json_encode($status),
                );
                $this->send_success($slug, $account_slug, $data);
            } elseif ($status->transaction_status == 'pending') {
                echo "Payment pending for order: " . $order_id;
            } else {
                echo "Payment failed for order: " . $order_id;
            }
        } catch (\Exception $e) {
            log_message('error', 'Midtrans Error: ' . $e->getMessage());
            echo "Error checking payment status: " . $e->getMessage();
        }
    }

    public function moyasar_callback($slug, $account_slug)
    {
        if (isset($_GET['id'], $_GET['amount']) && !empty($_GET['id']) && !empty($_GET['amount'])) :

            if ($_GET['status'] == 'failed') {
                $this->session->set_flashdata('error', 'Payment Failed');
                $this->send_failed($slug, $account_slug, $data = []);
            } else {

                $moyasar = isJson($this->settings['moyasar_config']) ? json_decode($this->settings['moyasar_config']) : '';
                $apiKey = isset($moyasar->moyasar_secret_key) && !empty($moyasar->moyasar_secret_key) ? $moyasar->moyasar_secret_key : ''; // 'sk_test_RpRLngrEnaXpkn4zwThM2Q72aECtzT5d2UCAnn3S';

                $paymentId = $_GET['id']; // Replace with the actual payment ID

                $url = "https://api.moyasar.com/v1/payments/$paymentId";

                $client = new Client();

                try {
                    $response = $client->request('GET', $url, [
                        'headers' => [
                            'Authorization' => 'Basic ' . base64_encode($apiKey . ':'),
                        ],
                    ]);

                    $statusCode = $response->getStatusCode();
                    $responseData = json_decode($response->getBody());

                    if (!empty($responseData->status) && $responseData->status == 'paid') :
                        $data = array(
                            'currency' => $responseData->currency,
                            'amount' => $responseData->amount / 100,
                            'txn_id' => $responseData->id,
                            'status' => 'success',
                            'payment_type' => 'moyasar',
                            'all_info' => json_encode($responseData),
                        );
                        $this->send_success($slug, $account_slug, $data);
                    else :
                        $this->send_failed($slug, $account_slug, $data = []);
                    endif;
                } catch (GuzzleHttp\Exception\RequestException $e) {
                    // Handle request exceptions (e.g., connection issues, timeouts)
                    echo 'Request failed: ' . $e->getMessage();
                } catch (Exception $e) {
                    // Handle general exceptions
                    echo 'Error: ' . $e->getMessage();
                }
            }
        else :
            $this->send_failed($slug, $account_slug, $data = []);
        endif;
    }
    public function netseasy($slug, $account_slug)
    {
        $data = [];
        $data['page_title'] = 'Netseasy';
        $data['account_slug'] = $account_slug;
        $data['slug'] = $slug;
        $data['settings'] = settings();;
        $data['paymentId'] = isset($_GET['paymentId']) ? $_GET['paymentId'] : '';
        $this->load->view('backend/payments/netseasy_payment', $data);
    }

    public function netseasy_verify($slug, $account_slug)
    {
        $data = [];
        $data['account_slug'] = $account_slug;
        $data['slug'] = $slug;
        $u_info = get_all_user_info_slug($slug);
        $package = $this->admin_m->get_package_info_by_slug($account_slug);
        $netseasy = isJson($this->settings['netseasy_config']) ? json_decode($this->settings['netseasy_config']) : '';
        $paymentId = $_GET['paymentId'];
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://test.api.dibspayment.eu/v1/payments/{$paymentId}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "Authorization: {$netseasy->netseasy_secret_key}",
                "CommercePlatformTag: {$this->settings['site_name']}"
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);
        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            $netseasy = json_decode($response)->payment;
            if (isset($netseasy->paymentId) && !empty($netseasy->paymentId)) :
                $data = array(
                    'currency' => $netseasy->orderDetails->currency,
                    'amount' => $netseasy->orderDetails->amount / 100,
                    'txn_id' => $netseasy->paymentId,
                    'status' => 'success',
                    'payment_type' => 'netseasy',
                    'all_info' => json_encode($netseasy),
                );
                $this->send_success($slug, $account_slug, $data);
            else :
                $this->send_failed($slug, $account_slug, $data = []);
            endif;
        }
    }




    function success($slug, $account_slug)
    {
        $data = array();
        $statusMsg = '';
        //get payment data from paypal url
        $paypalInfo = $this->input->get();

        $settings = settings();
        $package_info = get_package_info_by_slug($account_slug); //get package info by slug
        $u_info = get_all_user_info_slug($slug); //get user info by id from paypal url

        if (!empty($paypalInfo["amt"])) {
            $data = array(
                'currency' => $paypalInfo["cc"],
                'amount' => $paypalInfo["amt"],
                'txn_id' => $paypalInfo["tx"],
                'status' => $paypalInfo["st"],
                'payment_type' => 'paypal',
                'all_info' => json_encode([]),
            );

            $this->send_success($slug, $account_slug, $data);
        } else {
            $this->send_failed($slug, $account_slug);
        }
    }


    //paypal payment cancel
    function cancel($slug = '')
    {
        if (isset($_GET['slug'])) {
            $slug = $_GET['slug'];
        } else {
            $slug = $slug;
        }
        $this->session->set_flashdata('payment_error', 'Payment cancel');
        redirect(base_url('stripe-payment-success/' . $slug));
    }


    /* *******  Razorpay payment method **********
================================================== */
    function razorpay_payment()
    {
        $statusMsg = '';
        $data = array();

        if (!empty($this->input->post('razorpay_payment_id'))) {
            //get payment data from paypal url
            $razorpay_payment_id = $this->input->post('razorpay_payment_id');
            $package_id = $this->input->post('product_id');
            $settings = settings();
            $package_info = get_package_info_by_id($package_id); //get package info by id
            $u_info = get_all_user_info_slug($this->input->post('username')); //get user info by id from paypal url


            $amount = $this->input->post('totalAmount');

            $keys = array(
                'key_id' => $settings['razorpay_key_id'],
                'secret_key' => $settings['razorpay_key'],
            );


            $data = array(
                'amount' => $amount * 100,
                'currency' => get_currency('currency_code'),
            );

            $success = false;
            $error = '';

            try {
                $ch = $this->curl_handler($razorpay_payment_id, $data, $keys);
                //execute post
                $result = curl_exec($ch);

                $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                if ($result === false) {
                    $success = false;
                    $error = 'Curl error: ' . curl_error($ch);
                } else {
                    $response_array = json_decode($result, true);
                    //Check success response
                    if ($http_status === 200 and isset($response_array['error']) === false) {
                        $success = true;
                    } else {
                        $success = false;
                        if (!empty($response_array['error']['code'])) {
                            $error = $response_array['error']['code'] . ':' . $response_array['error']['description'];
                        } else {
                            $error = 'RAZORPAY_ERROR:Invalid Response <br/>' . $result;
                        }
                    }
                }
                //close curl connection
                curl_close($ch);
            } catch (Exception $e) {
                $success = false;
                $error = 'Request to Razorpay Failed';
                echo json_encode(['st' => 0, 'msg' => $$error]);
            }

            if ($success === true) {
                $payment_data = array(
                    'user_id' => $u_info['user_id'],
                    'account_type' => $package_info['id'],
                    'price' => $amount,
                    'currency_code' => 'INR',
                    'txn_id' => $razorpay_payment_id,
                    'status' => 'Authorized',
                    'payment_type' => 'razorpay',
                    'created_at' => d_time(),
                );
                $insert = $this->common_m->insert($payment_data, 'payment_info');

                if ($insert) :
                    $statusMsg .= '<h4>' . lang("thank_you_for_your_payment") . '</h4>';
                    $statusMsg .= '<h5>' . lang("the_transaction_was_successfull") . '</h5>';
                    $statusMsg .= "<p>" . lang('package') . ": {$package_info['package_name']}</p>";
                    $statusMsg .= "<p>" . lang('transaction_id') . ": {$razorpay_payment_id}</p>";
                    $statusMsg .= "<p>" . lang('total') . ": {$amount} INR</p>";


                    $this->common_m->update(array('is_payment' => 1, 'is_expired' => 0, 'is_request' => 0, 'start_date' => d_time(), 'end_date' => add_year($package_info['package_type'], $package_info['duration'], $package_info['duration_period']), 'account_type' => $package_info['id']), $u_info['user_id'], 'users');

                    $this->session->set_flashdata('payment_msg', $statusMsg);
                    $data['status'] = 1;
                    $data['msg'] = $statusMsg;
                    echo json_encode($data);
                endif;
            } else {
                $msg = 'Payment Canceled';
                echo json_encode(['st' => 0, 'msg' => $msg]);
            } //success === true


        } else {
            $msg = 'An error occured. Contact site administrator, please!';
            echo json_encode(['st' => 0, 'msg' => $msg]);
        }
    }


    private function curl_handler($payment_id, $data, $keys)
    {
        $url            = 'https://api.razorpay.com/v1/payments/' . $payment_id . '/capture';
        $key_id         = $keys['key_id'];
        $key_secret     = $keys['secret_key'];
        $params = http_build_query($data);
        //cURL Request
        $ch = curl_init();
        //set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERPWD, $key_id . ':' . $key_secret);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        return $ch;
    }


    /* ************* Start stripe Payment
    ================================================== */
    public function stripe_payment($slug, $account_slug)
    {
        $data = array();
        $data['page_title'] = "Stripe Payment";
        $data['page'] = "Payment";
        $data['slug'] = $slug;
        if (empty($slug)) {
            redirect(base_url('404'));
        }
        $data['account_type'] = $account_slug;
        $data['u_info'] = get_all_user_info_slug($slug);
        $data['total_amount'] = $this->total_amount();

        $data['package'] = $this->admin_m->get_package_info_by_slug($account_slug);
        $data['main_content'] = $this->load->view('backend/payments/stripe_payment_form', $data, TRUE);
        $this->load->view('backend/index', $data);
    }


    public function stripe_success($slug)
    {
        $data = array();
        $data['page_title'] = "Payment success";
        $data['page'] = "Payment";
        $data['slug'] = $slug;
        $data['msg'] = $this->session->flashdata('payment_msg') ? $this->session->flashdata('payment_msg') : '';
        $data['u_info'] = get_all_user_info_slug($slug);
        $data['main_content'] = $this->load->view('backend/payments/payment_success', $data, TRUE);
        $this->load->view('backend/index', $data);
    }

    public function payment()
    {
        $data = array();
        $statusMsg = '';

        if (!empty($this->input->post('stripeToken'))) {
            $amount = $this->input->post('amount');
            $package_id = $this->input->post('package_id');
            $username = $this->input->post('username');
            $name = $this->input->post('stripe_name');
            $email = $this->input->post('stripe_email');

            $u_info = get_all_user_info_slug($username);
            $package_info = $this->admin_m->get_package_info_by_id($package_id);

            $params = array(
                'amount' => $amount * 100,
                'currency' => CURRENCY_CODE,
                'description' => 'Charge for ' . $this->settings['site_name'] . ' Registrations',
                'source' => $this->input->post('stripeToken'),
                'metadata' => array(
                    'product_id' => $package_id,
                    'shipping' => 'express'
                )
            );
            $jsonData = $this->get_curl_handle($params);
            $resultJson = json_decode($jsonData, true);

            if (!empty($resultJson['error'])) :
                echo "<pre>";
                print_r($resultJson['error']);
                exit();
            else :

                if ($resultJson['amount_refunded'] == 0 && empty($resultJson['failure_code']) && $resultJson['paid'] == 1 && $resultJson['captured'] == 1) {
                    // Order details  
                    $transactionID = $resultJson['balance_transaction'];
                    $currency = $resultJson['currency'];
                    $payment_status = $resultJson['status'];
                    $amount_captured = $resultJson['amount_captured'] / 100;

                    // If the order is successful 
                    if ($payment_status == 'succeeded') {

                        $data = array(
                            'currency' => $currency,
                            'amount' => $amount_captured,
                            'txn_id' => $transactionID,
                            'status' => $payment_status,
                            'payment_type' => 'stripe',
                            'all_info' => json_encode(['receipt_url' => $resultJson['receipt_url']]),
                        );

                        $this->send_success($u_info['username'], $package_info['slug'], $data);
                    } else {
                        $msg = "Your Payment has Failed!";
                        $this->send_failed($u_info['username'], $package_info['slug'], $data, $msg);
                    }
                } else {
                    $msg = isset($resultJson['message']) ? $resultJson['message'] : "Transaction has been failed!";
                    $this->send_failed($u_info['username'], $package_info['slug'], $data, $msg);
                }
            endif;
        } else {
            $statusMsg = "Error on form submission.";
            $this->send_failed('', '', $data, $msg = '');
        }
    }

    // get curl handle method
    private function get_curl_handle($data)
    {
        $url = 'https://api.stripe.com/v1/charges';
        $key_secret = $this->config->item('secret_key');
        //cURL Request
        $ch = curl_init();
        //set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERPWD, $key_secret);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_POST, 1);
        $params = http_build_query($data);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        $output = curl_exec($ch);
        return $output;
    }


    /* end stripe payment
    ================================================== */



    /*********  Offline Payment
    ================================================== */

    public function offline_payment($slug, $account_slug)
    {
        $statusMsg = '';
        $data = array();
        $data['page_title'] = "Offline Payment";
        $data['slug'] = $slug;
        $data['page'] = 'Payment';
        $txn_id = 'op-' . random_string('alnum', 4) . '-' . random_string('numeric', 4);
        $currency = CURRENCY_CODE;

        $u_info = get_all_user_info_slug($slug);
        $package_info = get_package_info_by_slug($account_slug);
        $send = $this->offline_payment_request_mail($u_info, $package_info, $txn_id);

        if ($send) {
            $off_data = array(
                'txn_id' => $txn_id,
                'username' => $slug,
                'user_id' => $u_info['user_id'],
                'email' => $u_info['email'],
                'package' => $package_info['package_name'],
                'price' => $package_info['price'],
                'status' => 0,
                'created_at' => d_time(),
            );
            $this->admin_m->insert($off_data, 'offline_payment');


            //update payment info
            __update_payment($u_info['user_id'], $package_info['id']);



            $user_data = array(
                'is_request' => 1,
                'is_payment' => 0,
                'account_type' => $package_info['id'],
            );
            $this->admin_m->update($user_data, $u_info['user_id'], 'users');

            $statusMsg .= '<h4>Thanks for your Payment Request!</h4>';
            $statusMsg .= '<h5>Payement Request details are given below:</h5>';
            $statusMsg .= "<p>" . lang('package') . ": {$package_info['package_name']}</p>";
            $statusMsg .= "<p>Request ID: {$txn_id}</p>";
            $statusMsg .= "<p>" . lang('total') . ": {$package_info['price']} {$currency}</p>";
            $this->session->set_flashdata('success_msg', $statusMsg);
            redirect(base_url('payment/successMsg/' . $u_info['username']));
            exit();
        } else {
            $statusMsg = 'Somethings Were Wrong!! Try again';
            $this->session->set_flashdata('payment_error', $statusMsg);
        }
        redirect(base_url('payment/successMsg/' . $u_info['username']));
    }

    public function offline_payment_request($slug, $account_slug)
    {
        is_test();
        $is_required = $_POST['is_txn_required'];
        $this->form_validation->set_rules('username', 'Username', 'trim|xss_clean|required');
        $this->form_validation->set_rules('package_slug', 'Package Slug', 'trim|xss_clean|required');

        if (isset($is_required) && $is_required == 1) :
            $this->form_validation->set_rules('transaction_id', lang('transaction_id'), 'trim|xss_clean|required');
        endif;

        if ($this->form_validation->run() == FALSE) {
            $this->session->set_flashdata('error', validation_errors());
            redirect($_SERVER['HTTP_REFERER']);
        } else {
            $statusMsg = '';
            $u_info = get_all_user_info_slug($slug);
            $package_info = get_package_info_by_slug($account_slug);
            $off_data = array(
                'txn_id' => $_POST['transaction_id'],
                'username' => $slug,
                'user_id' => $u_info['user_id'],
                'email' => $u_info['email'],
                'package' => $package_info['package_name'],
                'price' => $package_info['price'],
                'status' => 0,
                'created_at' => d_time(),
            );
            $insert = $this->admin_m->insert($off_data, 'offline_payment');


            $user_data = array(
                'is_request' => 1,
                'is_payment' => 0,
                'account_type' => $package_info['id'],
            );
            $this->admin_m->update($user_data, $u_info['user_id'], 'users');

            if ($insert) {
                $send = @$this->offline_payment_request_mail($u_info, $account_slug, $_POST['transaction_id']);
                $statusMsg .= '<h4>' . lang("thank_you_for_your_payment") . '</h4>';
                $statusMsg .= '<h5>' . lang("payment_request_details") . '</h5>';
                $statusMsg .= "<p>" . lang('package') . ": {$package_info['package_name']}</p>";
                $statusMsg .= "<p>" . lang('request_id') . ": {$off_data['txn_id']}</p>";
                $statusMsg .= "<p>" . lang('total') . ": {$package_info['price']}</p>";
                $this->session->set_flashdata('success_msg', $statusMsg);
                redirect(base_url('payment/successMsg/' . $u_info['username']));
                exit();
            } else {
                $statusMsg = 'Somethings Were Wrong!! Try again';
                $this->session->set_flashdata('payment_error', $statusMsg);
            }
            redirect(base_url('payment/successMsg/' . $u_info['username']));
        }
    }

    public function text_email_verification()
    {
        $data = [
            'username' => 'phplime',
            'email' => '<EMAIL>',
            'package_name' => 'basic',
            'price' => '50',
            'expire_date' => '10-10-2023',
            'user_id' => '8' ?? '',
            'txnid' => '1234LKIF55LO' ?? '',
        ];
        $package = ['package_name' => 'basic', 'price' => '50.00'];
        $txn_id = '1234LKIF55LO';
        $password = '1234';
        $send = $this->email_m->send_payment_verified_email($data, 'Paypal');
        // $send = $this->offline_payment_request_mail($data,$package,$txn_id);
        echo "<pre>";
        print_r($send);
        exit();
    }

    protected function offline_payment_request_mail($u_info, $package_info, $txn_id)
    {
        if (isset($this->settings['is_dynamic_mail']) && $this->settings['is_dynamic_mail'] == 1) :
            $mailData =  [
                'site_name' => $this->settings['site_name'],
                'username' => $u_info['username'] ?? '',
                'package_name' => $package_info['package_name'] ?? '',
                'email' => $u_info['email'] ?? '',
                'txnid' => $txn_id ?? '',
                'price' => $package_info['price'] ?? '',
            ];
            $send = @$this->email_m->send_global_mail($mailData, $this->settings['smtp_mail'], 'offline_payment_request_mail');
        else :
            $send = @$this->email_m->offline_payment_request_mail($u_info['username'], $package_info['slug'], $txn_id);
        endif;

        return $send;
    }

    /*----------------------------------------------
  			Payment success method
----------------------------------------------*/

    public function send_success($slug, $account_slug, $data)
    {
        $statusMsg = '';
        $package_info = get_package_info_by_slug($account_slug); //get package info by slug
        $u_info = get_all_user_info_slug($slug);


        $check = $this->affiliate_m->check_existing_payment_by_subscriber($u_info['user_id'], $package_info['id'], $data['txn_id']);

        $this->admin_m->update_by_user_id(['is_running' => 0], $u_info['user_id'], 'payment_info');

        if (!empty($check)) {
            $ref_id = $check->ref_id;
            $package_price = !empty($check->package_price) ? $check->package_price : $check->price;
            $id = $check->id;
        } else {
            $ref_id = 0;
            $id = 0;
            $package_price = $package_info['price'];
        }



        $data_info = array(
            'user_id' => $u_info['user_id'],
            'account_type' => $package_info['id'],
            'price' => $data['amount'],
            'package_price' => $package_price,
            'currency_code' => $data['currency'],
            'status' => $data['status'],
            'txn_id' => $data['txn_id'],
            'payment_type' => $data['payment_type'],
            'all_info' => json_encode($data['all_info']),
            'created_at' => d_time(),
            'expire_date' => add_year($package_info['package_type'], $package_info['duration'], $package_info['duration_period']),
            'is_running' => 1,
            'is_self' => 1,
        );

        if ($id == 0) {
            $insert = $this->common_m->insert($data_info, 'payment_info');
        } else {
            $insert = $this->common_m->update($data_info, $id, 'payment_info');
        }
        if ($insert) :

            __update_payment($data_info['user_id'], $data_info['account_type'], $data_info['price']);



            $statusMsg .= '<h4>' . lang("thank_you_for_your_payment") . '</h4>';
            $statusMsg .= '<h5>' . lang("the_transaction_was_successfull") . '</h5>';
            $statusMsg .= "<p" . lang('package') . " {$package_info['package_name']}</p>";
            $statusMsg .= "<p>" . lang('transaction_id') . ": {$data['txn_id']}</p>";
            $statusMsg .= "<p>" . lang('total') . ": {$data['amount']} {$data['currency']}</p>";

            $this->session->set_flashdata('success_msg', $statusMsg);


            $this->common_m->update(array('is_payment' => 1, 'is_expired' => 0, 'is_request' => 0, 'start_date' => d_time(), 'end_date' => add_year($package_info['package_type'], $package_info['duration'], $package_info['duration_period']), 'account_type' => $package_info['id']), $u_info['user_id'], 'users');

            if (isLocalHost() == false):
                $this->email_m->send_payment_verified_email($data_info, $data['payment_type']); // send payment transaction succesfull mail with transaction_id
            endif;


            redirect(base_url('payment/successMsg/' . $u_info['username']));
        endif;
    }

    public function successMsg($slug)
    {
        $data = array();
        $data['page_title'] = "Payment success";
        $data['page'] = "Payment";
        $data['slug'] = $slug;
        $statusMsg = '';
        if (!empty($this->session->flashdata('success_msg'))) {
            $statusMsg .= '<h4>' . lang("thank_you_for_your_payment") . '</h4>';
            $statusMsg .= '<h5>The transaction was successful. </h5>';
            $data['msg'] = $statusMsg;
        } else {

            $data['msg'] = $this->session->flashdata('success_msg') ? $this->session->flashdata('success_msg') : '';
        }
        $data['u_info'] = get_all_user_info_slug($slug);

        $data['main_content'] = $this->load->view('backend/payments/success_msg', $data, TRUE);
        $this->load->view('backend/index', $data);
    }





    public function send_failed($slug = '', $account_slug = '', $data = [], $msg = '')
    {
    
        $statusMsg = '';
        if (!empty($data) && !empty($account_slug)) :
            $package_info = get_package_info_by_slug($account_slug); //get package info by slug
            $u_info = get_all_user_info_slug($slug);
            $data_info = array(
                'user_id' => $u_info['user_id'],
                'account_type' => $package_info['id'],
                'price' => !empty($data['amount']) ? $data['amount'] : $package_info['price'],
                'currency_code' => !empty($data['currency']) ? $data['currency'] : get_currency('currency_code'),
                'status' => 'failed',
                'txn_id' => $data['txn_id'],
                'payment_type' => $data['payment_type'],
                'all_info' => json_encode($data['all_info']),
                'created_at' => d_time(),
            );
            $msg = isset($msg) && !empty($msg) ? "<p>Error: {$data['msg']}</p>" : '';
            $insert = $this->common_m->insert($data_info, 'payment_info');
            if ($insert) :
                $statusMsg .= '<h4>Sorry Payment Failed</h4>';
                $statusMsg .= '<h5>The transaction was unsuccessful. Transaction details are given below:</h5>';
                $statusMsg .= "<p>" . lang('package') . ": {$package_info['package_name']}</p>";
                $statusMsg .= "<p>" . lang('transaction_id') . ": {$data['txn_id']}</p>";
                $statusMsg .= "<p>" . lang('total') . ": {$data['amount']} {$data['currency']}</p>";
                $statusMsg .= $msg;
                $this->session->set_flashdata('payment_error', $statusMsg);
                redirect(base_url('stripe-payment-success/' . $u_info['username']));
            endif;
        else :
            $statusMsg .= '<h4>Sorry Payment Failed</h4>';
            $statusMsg .= '<h5>The transaction was unsuccessful.</h5>';
            $statusMsg .= $msg;
            $this->session->set_flashdata('payment_error', $statusMsg);
            redirect(base_url('stripe-payment-success/' . $slug));
        endif;
    }

    public function paytm_verify()
    {
        $mkey = $_GET['key'];
        require_once("vendor/paytm/paytmchecksum/PaytmChecksum.php");
        $checksum = (!empty($_POST['CHECKSUMHASH'])) ? $_POST['CHECKSUMHASH'] : '';
        unset($_POST['CHECKSUMHASH']);
        $verifySignature = PaytmChecksum::verifySignature($_POST, $mkey, $checksum);
        if ($verifySignature) {
            $data = array(
                'currency' => $_POST['CURRENCY'],
                'amount' => $_POST['TXNAMOUNT'],
                'txn_id' => $_POST['TXNID'],
                'status' => $_POST['STATUS'],
                'payment_type' => 'paytm',
                'all_info' => json_encode(['bank_name' => $_POST['BANKNAME'], 'bank_txn_id' => $_POST['BANKTXNID'], 'gateway' => $_POST['GATEWAYNAME'], 'payment_mode' => $_POST['PAYMENTMODE']]),
            );
            $this->send_success($_GET['slug'], $_GET['account_slug'], $data);
        } else {
            $this->send_failed($_GET['slug'], $_GET['account_slug'], $data);
        }
    }


    public function stripe_fpx()
    {
        $settings = settings();
        $stripe_fpx = json_decode($settings['fpx_config']);
        $stripe = $this->input->get();
        \Stripe\Stripe::setApiKey($stripe_fpx->fpx_secret_key);

        $intent = \Stripe\PaymentIntent::retrieve($stripe['payment_intent']); //PAYMENT_INTENT_ID
        $charges = $intent->charges->data;
        if ($stripe['redirect_status'] == "succeeded") :
            $bank_name = $charges[0]->payment_method_details->fpx->bank;
            $bank_txn = $charges[0]->payment_method_details->fpx->transaction_id;
            $data = array(
                'currency' => $charges[0]->currency,
                'amount' => $charges[0]->amount_captured / 100,
                'txn_id' => $charges[0]->balance_transaction,
                'status' => $charges[0]->status,
                'payment_type' => 'stripe_fpx',
                'all_info' => json_encode(['bank_name' => $bank_name, 'bank_txn_id' => $bank_txn]),
            );
            $this->send_success($stripe['slug'], $stripe['account_slug'], $data);
        elseif ($stripe['redirect_status'] == "failed") :
            $this->send_failed($_GET['slug'], $_GET['account_slug'], $data = []);
        endif;
    }

    public function mercado()
    {
        $settings = settings();
        $mercado = !empty($settings['mercado_config']) ? json_decode($settings['mercado_config']) : '';

        if ($_GET['payment_id'] != "null" && $_GET['merchant_order_id'] != "null") :
            $respuesta = array(
                'Payment' => $_GET['payment_id'],
                'Status' => $_GET['status'],
                'MerchantOrder' => $_GET['merchant_order_id']
            );
            MercadoPago\SDK::setAccessToken($mercado->access_token);
            $merchant_order = $_GET['payment_id'];

            $payment = MercadoPago\Payment::find_by_id($merchant_order);
            $merchant_order = MercadoPago\MerchantOrder::find_by_id($payment->order->id);

            $data = array(
                'currency' => $payment->currency_id,
                'amount' => $payment->transaction_details->total_paid_amount,
                'txn_id' => $_GET['preference_id'],
                'status' => $payment->status,
                'payment_type' => 'mercadopago',
                'all_info' => $merchant_order->payments,
            );
            $this->send_success($_GET['slug'], $_GET['account_slug'], $data);
            exit();
        else :
            $paymentData = array(
                'currency' => get_currency('currency_code'),
                'amount' => '',
                'txn_id' => '**********',
                'status' => 'error',
                'payment_type' => 'mercadopago',
                'all_info' => json_encode([]),
            );
            $this->send_failed($_GET['slug'], $_GET['account_slug'], $paymentData);
            exit();
        endif;

        echo "<pre>";
        print_r($_GET);
        exit();
    }


    /*----------------------------------------------
FLUTTERWAVE
----------------------------------------------*/



    public function flutterwave_create_transaction()
    {
        $post = $_POST;
        $data = array(
            'amount' => $post['amount'],
            'customer_email' => $post['customer_email'],
            'redirect_url' => base_url("payment/flutterwave_payment_status/?slug={$post['slug']}&account_slug={$post['account_slug']}"),
            'payment_plan' => $post['payment_plan'],
            'csrf_test_name' => $this->security->get_csrf_hash(),
            'slug' => $post['slug'],
            'account_slug' => $post['account_slug'],
        );
        $this->response = $this->payment_m->create_flutterwave_payment($data);

        if (!empty($this->response) || $this->response != '') {
            $this->response = json_decode($this->response, 1);
            if (isset($this->response['status']) && $this->response['status'] == 'success') {
                redirect($this->response['data']['link']);
            } else {
                $this->session->set_flashdata('message_type', 'danger');
                $this->session->set_flashdata('message', 'API returned error >> ' . $this->response['message']);
                redirect(base_url('flutterwave/'));
            }
        }
    }
    public function flutterwave_payment_status()
    {

        $settings = settings();

        if (!empty($settings['flutterwave_config'])) :
            $flutterwave = json_decode($settings['flutterwave_config']);
            $_ENV = [
                "PUBLIC_KEY" => $flutterwave->fw_public_key, // can be gotten from the dashboard
                "SECRET_KEY" => $flutterwave->fw_secret_key, // can be gotten from the dashboard
                "ENCRYPTION_KEY" => $flutterwave->encryption_key,
                "ENV" => $flutterwave->is_flutterwave_live == 0 ? "development" : "production",
            ];
        endif;


        $params = $this->input->get();
        if (empty($params)) {
            $paymentData = array(
                'currency' => get_currency('currency_code'),
                'amount' => '',
                'txn_id' => '**********',
                'status' => 'error',
                'payment_type' => 'flutterwave',
                'all_info' => json_encode([]),
            );

            $this->send_failed($_GET['slug'], $_GET['account_slug'], $paymentData);
        } elseif (isset($params['tx_ref']) && !empty($params['tx_ref'])) {
            $response = $this->payment_m->verify_flutterwave($params);
            if (!empty($response)) {
                if ($response['status'] == 'success' && isset($response['data']['charged_amount']) && (!$response['data']['charged_amount'] == '00' || !$response['data']['charged_amount'] == '0')) {


                    $data['customer_email']         = $response['data']['customer']['email'];
                    $data['txn_id']         = $response['data']['flw_ref'];
                    $data['amount']    = $response['data']['amount'];
                    $data['currency_code']  = $response['data']['currency'];
                    $data['status']         = $response['data']['status'];
                    $data['message']        = $response['message'];
                    $data['full_data']      = $response;
                    $paymentData = array(
                        'currency' => $response['data']['currency'],
                        'amount' => $response['data']['amount'],
                        'txn_id' => $response['data']['flw_ref'],
                        'status' => $response['data']['status'],
                        'payment_type' => 'flutterwave',
                        'all_info' => json_encode(['customer_email' => $data['customer_email'], 'ip' => $data['customer_email'], 'txid' => $data['txn_id']]),
                    );
                    $this->send_success($_GET['slug'], $_GET['account_slug'], $paymentData);
                } elseif ((isset($params['cancelled']) && $params['cancelled'] == true)) {
                    $paymentData = array(
                        'currency' => get_currency('currency_code'),
                        'amount' => '',
                        'txn_id' => '**********',
                        'status' => 'cancelled',
                        'payment_type' => 'flutterwave',
                        'all_info' => json_encode([]),
                    );
                    $this->send_failed($_GET['slug'], $_GET['account_slug'], $paymentData);
                } elseif ($response['status'] == 'error') {
                    $paymentData = array(
                        'currency' => $response['data']['currency'],
                        'amount' => $response['data']['amount'],
                        'txn_id' => $response['data']['tx_ref'],
                        'status' => $response['data']['status'],
                        'payment_type' => 'flutterwave',
                        'all_info' => json_encode(['customer_email' => $response['data']['custemail'], 'narration' => $response['data']['narration'], 'ip' => $response['data']['custemail'], 'txid' => $response['data']['custemail']]),
                    );
                    $this->send_failed($_GET['slug'], $_GET['account_slug'], $paymentData);
                    $this->load->view('flutterwave/payment_status', $data = []);
                }
            } else {
                $paymentData = array(
                    'currency' => get_currency('currency_code'),
                    'amount' => '',
                    'txn_id' => '**********',
                    'status' => 'error',
                    'payment_type' => 'flutterwave',
                    'all_info' => json_encode([]),
                );

                $this->send_failed($_GET['slug'], $_GET['account_slug'], $paymentData);
            }
        }
    }


    /*----------------------------------------------
                      Paystack Payment gateways
    ----------------------------------------------*/


    public function verify_payment($ref)
    {


        $result = array();
        $slug = isset($_GET['user']) ? $_GET['user'] : '';
        $account_slug = isset($_GET['package']) ? $_GET['package'] : '';
        $settings = settings();
        $paystack = !empty($settings['paystack_config']) ? json_decode($settings['paystack_config']) : '';
        $paystack_secret_key = !empty($paystack->paystack_secret_key) ? $paystack->paystack_secret_key : '';

        $url = 'https://api.paystack.co/transaction/verify/' . $ref;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt(
            $ch,
            CURLOPT_HTTPHEADER,
            [
                'Authorization: Bearer ' . $paystack_secret_key
            ]
        );
        $request = curl_exec($ch);

        curl_close($ch);
        //
        if ($request) {
            $result = json_decode($request, true);
            // print_r($result);
            if ($result) {
                if ($result['data']) {
                    //something came in
                    if ($result['data']['status'] == 'success') {

                        //echo "Transaction was successful";
                        $this->paystack_success($ref, $slug, $account_slug, 'success');
                    } else {
                        // the transaction was not successful, do not deliver value'
                        // print_r($result);  //uncomment this line to inspect the result, to check why it failed.
                        $this->paystack_success($ref, $slug, $account_slug, 'fail');
                    }
                } else {

                    //echo $result['message'];
                    $this->paystack_success($ref, $slug, $account_slug, 'fail');
                }
            } else {
                //print_r($result);
                //die("Something went wrong while trying to convert the request variable to json. Uncomment the print_r command to see what is in the result variable.");
                $this->paystack_success($ref, $slug, $account_slug, 'fail');
            }
        } else {
            //var_dump($request);
            //die("Something went wrong while executing curl. Uncomment the var_dump line above this line to see what the issue is. Please check your CURL command to make sure everything is ok");
            $this->paystack_success($ref, $slug, $account_slug, 'fail');
        }
    }

    public function paystack_success($ref, $slug, $account_slug, $type = 'success')
    {
        $data = array();
        $settings = settings();
        $paystack = !empty($settings['paystack_config']) ? json_decode($settings['paystack_config']) : '';
        $paystack_secret_key = !empty($paystack->paystack_secret_key) ? $paystack->paystack_secret_key : '';
        if ($type == "success") :
            $info = $this->getPaymentInfo($ref, $paystack_secret_key);
            $data = [
                'amount' => $info['amount'] / 100,
                'currency' => $info['currency'],
                'status' => $info["status"],
                'txn_id' => $info["reference"],
                'payment_type' => 'paystack',
                'all_info' => $info,
            ];

            $this->send_success($slug, $account_slug, $data);
        else :
            $data = [
                'amount' => 0,
                'currency' => 0,
                'status' => 'Failed',
                'txn_id' => '**********',
                'payment_type' => 'paystack',
                'all_info' => '',
            ];

            $this->send_failed($slug, $account_slug, $data);
        endif;
    }

    private function getPaymentInfo($ref, $secret_key)
    {

        $result = array();
        $url = 'https://api.paystack.co/transaction/verify/' . $ref;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        //
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt(
            $ch,
            CURLOPT_HTTPHEADER,
            [
                'Authorization: Bearer ' . $secret_key
            ]
        );
        $request = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($request, true);
        return $result['data'];
    }



    /*----------------------------------------------
                Pagadito Payment Gateway
    ----------------------------------------------*/

    public function pagadito_success()
    {
        $_ENV = $this->session->userdata('pagadito_data');
        if (isset($_GET)) {
            $u_info = get_all_user_info_slug(pagadito('slug'));
            $package_info = get_package_info_by_slug(pagadito('account_slug'));
            $response = $this->payment_m->pagadito_verify($_GET);
            if (!empty($response)) :
                if ($response['status'] == 'success') {
                    $currency = $response['details']->currency;
                    $amount = $response['details']->response->value->amount;
                    $txn_id = $response['details']->response->value->reference;

                    $data = array(
                        'currency' => $currency,
                        'amount' => $amount,
                        'txn_id' => $txn_id,
                        'status' => $response['status'],
                        'payment_type' => 'pagadito',
                        'all_info' => json_encode(['details' => $response['details']]),
                    );

                    $this->send_success($u_info['username'], $package_info['slug'], $data);
                } else {
                    $msg = $response['status'];
                    $this->send_failed($u_info['username'], $package_info['slug'], $data = [], $msg);
                }
            else :
                $msg = "Sorry transaction faild";
                $this->send_failed($u_info['username'], $package_info['slug'], $data = [], $msg);
            endif;
        }
    }

    // Pagadito inital
    public  function pagadito($slug, $account_slug)
    {
        require_once(APPPATH . 'libraries/Pagadito.php');
        $settings = settings();
        if (!empty($settings['pagadito_config'])) :
            $pagadito =  json_decode($settings['pagadito_config']);
            $envData = [
                'slug' => $slug,
                'account_slug' => $account_slug,
                "UID" => $pagadito->pagadito_uid,
                "WSK" => $pagadito->pagadito_wsk_key,
                "SANDBOX" => $pagadito->is_pagadito_live == 0 ? TRUE : FALSE,
            ];
            $this->session->set_tempdata('pagadito_data', $envData, 900);
            $_ENV = $this->session->userdata('pagadito_data');
        endif;
        if (isset($_POST["slug"]) && isset($_POST["amount"])) :
            $Pagadito = new Pagadito($_ENV['UID'], $_ENV['WSK']);
            if ($_ENV['SANDBOX']) {
                $Pagadito->mode_sandbox_on();
            }

            if ($Pagadito->connect()) {

                $Pagadito->currency(get_currency('currency_code'));

                $Pagadito->add_detail($_POST["cantidad1"], $_POST["descripcion1"], $_POST["precio1"], $_POST["url1"]);
                /*
             * Then we go on to add the details
             */
                // if ($_POST["cantidad1"] > 0) {
                //     $Pagadito->add_detail($_POST["cantidad1"], $_POST["descripcion1"], $_POST["precio1"], $_POST["url1"]);
                // }

                //Adding custom transaction fields
                $Pagadito->set_custom_param("param1", $_POST['slug']);
                $Pagadito->set_custom_param("param2", $_POST['account_slug']);
                // $Pagadito->set_custom_param("param3", "Valor de param3");
                // $Pagadito->set_custom_param("param4", "Valor de param4");
                // $Pagadito->set_custom_param("param5", "Valor de param5");

                //Enables the receipt of pre-authorized payments for the collection order.
                $Pagadito->enable_pending_payments();

                /*
             * Lo siguiente es ejecutar la transacción, enviandole el ern.
             *
             * A manera de ejemplo el ern es generado como un número
             * aleatorio entre 1000 y 2000. Lo ideal es que sea una
             * referencia almacenada por el Pagadito Comercio.
             */
                $ern = random_string('alnum', 4);
                if (!$Pagadito->exec_trans($ern)) {
                    /*
                 * En caso de fallar la transacción, verificamos el error devuelto.
                 * Debido a que la API nos puede devolver diversos mensajes de
                 * respuesta, validamos el tipo de mensaje que nos devuelve.
                 */
                    switch ($Pagadito->get_rs_code()) {
                        case "PG2001":
                            /*Incomplete data*/
                        case "PG3002":
                            /*Error*/
                        case "PG3003":
                            /*Unregistered transaction*/
                        case "PG3004":
                            /*Match error*/
                        case "PG3005":
                            /*Disabled connection*/
                        default:
                            echo "
                    <SCRIPT>
                    alert(\"" . $Pagadito->get_rs_code() . ": " . $Pagadito->get_rs_message() . "\");
                    location.href = 'index.php';
                    </SCRIPT>
                    ";
                            break;
                    }
                }
            } else {
                /*
             * En caso de fallar la conexión, verificamos el error devuelto.
             * Debido a que la API nos puede devolver diversos mensajes de
             * respuesta, validamos el tipo de mensaje que nos devuelve.
             */
                switch ($Pagadito->get_rs_code()) {
                    case "PG2001":
                        /*Incomplete data*/
                    case "PG3001":
                        /*Problem connection*/
                    case "PG3002":
                        /*Error*/
                    case "PG3003":
                        /*Unregistered transaction*/
                    case "PG3005":
                        /*Disabled connection*/
                    case "PG3006":
                        /*Exceeded*/
                    default:
                        echo "
                <SCRIPT>
                alert(\"" . $Pagadito->get_rs_code() . ": " . $Pagadito->get_rs_message() . "\");
                location.href = 'index.php';
                </SCRIPT>
                ";
                        break;
                }
            }

        else :
            echo "
        <script>
        alert('No ha llenado los campos adecuadamente.');
        location.href = 'index.php';
        </script>
        ";
        endif;
    }





    /*----------------------------------------------
                      End Pagodito
    ----------------------------------------------*/

    /*----------------------------------------------
                      Asaas Payment Gateway
    ----------------------------------------------*/

    /**
     * Processa pagamentos via Asaas
     */
    public function process_asaas_payment()
    {
        $order_id = $this->input->post('order_id', true);
        $amount = floatval($this->input->post('amount'));
        
        // Verificar se temos valores válidos
        if (empty($order_id)) {
            $this->output->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'ID do pedido não informado'
                ]));
            return;
        }
        
        if ($amount <= 0) {
            // Tentar recuperar o valor do pedido do banco de dados
            $amount = $this->ensure_valid_order_amount($order_id);
            
            // Se ainda for zero ou inválido, retornar erro
            if ($amount <= 0) {
                $this->output->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => false,
                        'message' => 'Valor do pedido inválido'
                    ]));
                return;
            }
        }

        // Adicionar log de diagnóstico
        error_log("[Asaas Payment] Processando pagamento para pedido {$order_id} no valor de {$amount}");
        
        // Log para debug
        error_log('[Asaas Payment] Iniciando processamento de pagamento');
        $this->security->csrf_verify = false;
        $post = $this->input->post();
        if(empty($post)) {
            error_log('[Asaas Payment] Erro: Requisição sem dados POST');
            echo json_encode(['status' => 'error', 'message' => 'Requisição inválida - Dados POST vazios']);
            return;
        }

        // NOVA VERIFICAÇÃO: Validar o valor do pagamento
        if(empty($post['amount']) || !is_numeric($post['amount']) || floatval($post['amount']) <= 0) {
            error_log('[Asaas Payment] Erro: Valor de pagamento inválido: ' . ($post['amount'] ?? 'vazio'));
            echo json_encode(['status' => 'error', 'message' => 'Valor de pagamento inválido. O valor deve ser maior que zero.']);
            return;
        }

        // VERIFICAÇÃO ESPECÍFICA: Valor mínimo para diferentes tipos de pagamento
        $amount_value = floatval($post['amount']);
        $payment_type = isset($post['payment_type']) ? $post['payment_type'] : 'CREDIT_CARD';

        if($payment_type === 'CREDIT_CARD' && $amount_value < 5.00) {
            error_log('[Asaas Payment] Erro: Valor mínimo para cartão de crédito: R$ 5,00. Valor informado: R$ ' . $amount_value);
            echo json_encode([
                'status' => 'error',
                'message' => 'O valor mínimo para pagamentos com cartão de crédito é R$ 5,00. Valor atual: R$ ' . number_format($amount_value, 2, ',', '.')
            ]);
            return;
        }

        if($payment_type === 'PIX' && $amount_value < 1.00) {
            error_log('[Asaas Payment] Erro: Valor mínimo para PIX: R$ 1,00. Valor informado: R$ ' . $amount_value);
            echo json_encode([
                'status' => 'error',
                'message' => 'O valor mínimo para pagamentos PIX é R$ 1,00. Valor atual: R$ ' . number_format($amount_value, 2, ',', '.')
            ]);
            return;
        }

        try {
            $settings = settings();
            $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
            if(empty($asaas) || empty($asaas->asaas_api_key)) {
                error_log('[Asaas Payment] Erro: Configuração Asaas inválida');
                echo json_encode(['status' => 'error', 'message' => 'Configuração do Asaas incompleta. Verifique as chaves de API.']);
                return;
            }
            $apiKey = $asaas->asaas_api_key;
            $sandbox = !(isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1);
            error_log('[Asaas Payment] Ambiente: ' . ($sandbox ? 'Sandbox' : 'Produção'));

            // Carregar nossa classe cliente
            require_once FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';
            
            // Inicializar o cliente Asaas
            $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);
            
            if (!$asaasClient->isReady()) {
                error_log('[Asaas Payment] Erro: Cliente Asaas não inicializado: ' . $asaasClient->getLastError());
                echo json_encode(['status' => 'error', 'message' => 'Erro ao inicializar conexão com Asaas']);
                return;
            }

            // Buscar dados do usuário autenticado
            $user_id = $this->session->userdata('user_id');
            $is_anonymous = empty($user_id);
            
            // Log para debug
            error_log('[Asaas Payment] Usuário logado: ' . ($is_anonymous ? 'Não' : 'Sim, ID: ' . $user_id));
            
            if($is_anonymous) {
                $user_id = $this->session->userdata('temp_user_id');
                if(empty($user_id)) {
                    $user_id = rand(10000, 99999);
                    $this->session->set_userdata('temp_user_id', $user_id);
                }
                error_log('[Asaas Payment] Usuário anônimo, usando ID temporário: ' . $user_id);
            }
            
            // Buscar dados completos do usuário
            $user_info = !$is_anonymous ? $this->common_m->get_user_info_by_id($user_id) : [];
            
            // Verificar telefone - prioridade: 1) formulário 2) banco de dados
            $phone = '';
            
            // Obter dados do cliente do formulário (compatibilidade com novos e antigos campos)
            $customer_data = [
                'name' => $this->input->post('card_holder_name') ?: $this->input->post('holderName'),
                'email' => $this->input->post('card_holder_email') ?: $this->input->post('email'),
                'phone' => $this->input->post('card_holder_phone') ?: $this->input->post('holderPhone'),
                'cpf' => $this->input->post('card_holder_cpf') ?: $this->input->post('holderCpf'),
                'address' => $this->input->post('card_holder_address') ?: $this->input->post('address'),
                'postal_code' => $this->input->post('card_holder_postal_code') ?: $this->input->post('postal_code')
            ];

            // Log para debug dos dados recebidos
            error_log('[Asaas Payment] Dados do cliente extraídos: ' . json_encode($customer_data));
            error_log('[Asaas Payment] Address Number recebido: ' . ($this->input->post('address_number') ?: 'VAZIO'));
            error_log('[Asaas Payment] POST completo: ' . json_encode($this->input->post()));

            // Verificar se o telefone foi enviado pelo formulário (compatibilidade com campos antigos)
            if(!empty($post['holderPhone'])) {
                $phone = preg_replace('/[^0-9]/', '', $post['holderPhone']);
                error_log('[Asaas Payment] Telefone do formulário (campo antigo): ' . $post['holderPhone']);
                error_log('[Asaas Payment] Telefone do formulário (formatado): ' . $phone);
            } elseif(!empty($customer_data['phone'])) {
                $phone = preg_replace('/[^0-9]/', '', $customer_data['phone']);
                error_log('[Asaas Payment] Telefone do formulário (novo campo): ' . $customer_data['phone']);
                error_log('[Asaas Payment] Telefone do formulário (formatado): ' . $phone);
            }
            
            // Se não tiver telefone no formulário mas o usuário estiver logado, buscar do banco
            if(empty($phone) && !empty($user_info)) {
                if(!empty($user_info['phone'])) {
                    $phone = preg_replace('/[^0-9]/', '', $user_info['phone']);
                    error_log('[Asaas Payment] Telefone do banco de dados (bruto): ' . $user_info['phone']);
                    error_log('[Asaas Payment] Telefone do banco de dados (formatado): ' . $phone);
                } else {
                    error_log('[Asaas Payment] Usuário logado não possui telefone cadastrado');
                }
            }
            
            // MELHORIA: Validar telefone melhorada para aceitar 10 ou 11 dígitos brasileiros
            if(empty($phone)) {
                error_log('[Asaas Payment] Erro: Telefone não fornecido');
                echo json_encode(['status' => 'error', 'message' => 'Por favor, informe um número de telefone válido (com DDD)']);
                return;
            }
            
            // Remover caracteres não numéricos
            $clean_phone = preg_replace('/[^0-9]/', '', $phone);
            
            // Validar se o telefone tem entre 10 e 11 dígitos (padrão brasileiro)
            if(strlen($clean_phone) < 10 || strlen($clean_phone) > 11) {
                error_log('[Asaas Payment] Erro: Telefone com formato inválido: ' . $clean_phone . ' (comprimento: ' . strlen($clean_phone) . ')');
                echo json_encode(['status' => 'error', 'message' => 'Número de telefone deve ter 10 ou 11 dígitos (formato: DDD + número)']);
                return;
            }
            
            // Se tiver 10 dígitos e o terceiro dígito for 9, adicionar o 9 extra para celular
            if(strlen($clean_phone) == 10 && substr($clean_phone, 2, 1) == '9') {
                // Assumir que é celular sem o 9 extra, então adicionar
                $clean_phone = substr($clean_phone, 0, 2) . '9' . substr($clean_phone, 2);
                error_log('[Asaas Payment] Telefone convertido para formato celular: ' . $clean_phone);
            }
            
            // Validar DDDs válidos brasileiros
            $ddd = substr($clean_phone, 0, 2);
            $valid_ddds = ['11','12','13','14','15','16','17','18','19','21','22','24','27','28','31','32','33','34','35','37','38','41','42','43','44','45','46','47','48','49','51','53','54','55','61','62','63','64','65','66','67','68','69','71','73','74','75','77','79','81','82','83','84','85','86','87','88','89','91','92','93','94','95','96','97','98','99'];
            
            if(!in_array($ddd, $valid_ddds)) {
                error_log('[Asaas Payment] Erro: DDD inválido: ' . $ddd);
                echo json_encode(['status' => 'error', 'message' => 'DDD inválido. Informe um DDD brasileiro válido']);
                return;
            }
            
            // Usar o telefone limpo e validado
            $phone = $clean_phone;
            error_log('[Asaas Payment] Telefone final validado: ' . $phone);
            
            // Completar dados obrigatórios se necessário - priorizar novos campos do formulário
            $user_info['name'] = !empty($customer_data['name']) ? $customer_data['name'] :
                                (!empty($user_info['name']) ? $user_info['name'] :
                                (!empty($post['holderName']) ? $post['holderName'] : ''));
            $user_info['email'] = !empty($customer_data['email']) ? $customer_data['email'] :
                                 (!empty($user_info['email']) ? $user_info['email'] :
                                 (isset($post['email']) ? $post['email'] : 'cliente'.$user_id.'@temp.com'));
            $user_info['cpf'] = !empty($customer_data['cpf']) ? $customer_data['cpf'] :
                               (!empty($user_info['cpf']) ? $user_info['cpf'] :
                               (isset($post['holderCpf']) ? $post['holderCpf'] : ''));
            $user_info['phone'] = $phone;
            $user_info['address'] = !empty($customer_data['address']) ? $customer_data['address'] :
                                   (!empty($user_info['address']) ? $user_info['address'] :
                                   ($this->input->post('address') ?: 'Endereço não informado'));
            $user_info['postal_code'] = !empty($customer_data['postal_code']) ? $customer_data['postal_code'] :
                                       ($this->input->post('postal_code') ?: '');
            
            // Verificar se cliente existe no Asaas
            $customer_id = $this->get_asaas_customer_id($user_id);

            // Se não existir, criar um novo
            if(!$customer_id) {
                // Validar dados obrigatórios antes de criar cliente
                $customer_name = !empty($user_info['name']) ? $user_info['name'] : 'Cliente ' . $user_id;
                $customer_email = !empty($user_info['email']) ? $user_info['email'] : 'cliente'.$user_id.'@temp.com';
                $customer_phone = !empty($user_info['phone']) ? preg_replace('/[^0-9]/', '', $user_info['phone']) : '11999999999';
                $customer_cpf = !empty($user_info['cpf']) ? preg_replace('/[^0-9]/', '', $user_info['cpf']) : '12345678909';

                // Validações específicas
                if (strlen($customer_phone) < 10) {
                    $customer_phone = '11999999999'; // Telefone padrão
                }

                if (strlen($customer_cpf) != 11) {
                    $customer_cpf = '12345678909'; // CPF padrão para sandbox
                }

                $customerData = [
                    'name' => $customer_name,
                    'email' => $customer_email,
                    'phone' => $customer_phone,
                    'cpfCnpj' => $customer_cpf,
                    'externalReference' => 'user_' . $user_id
                ];

                error_log('[Asaas Payment] Criando cliente no Asaas: ' . json_encode($customerData));

                try {
                    $customer = $asaasClient->createCustomer($customerData);

                    if(!$customer || !isset($customer->id)) {
                        $error_msg = $asaasClient->getLastError();
                        error_log('[Asaas Payment] Erro ao criar cliente: ' . $error_msg);

                        // Tentar diagnóstico mais detalhado
                        if (strpos($error_msg, 'cpfCnpj') !== false) {
                            error_log('[Asaas Payment] Erro relacionado ao CPF. Tentando com CPF alternativo...');
                            $customerData['cpfCnpj'] = '11122233344';
                            $customer = $asaasClient->createCustomer($customerData);
                        }

                        if(!$customer || !isset($customer->id)) {
                            echo json_encode([
                                'status' => 'error',
                                'message' => 'Erro ao criar cliente no Asaas: ' . $error_msg,
                                'debug' => [
                                    'customer_data' => $customerData,
                                    'asaas_error' => $error_msg
                                ]
                            ]);
                            return;
                        }
                    }

                    $customer_id = $customer->id;

                    // Salvar o customer_id para uso futuro
                    $this->update_asaas_customer_id($user_id, $customer_id);

                    error_log('[Asaas Payment] Cliente criado com sucesso: ' . $customer_id);

                } catch (Exception $e) {
                    error_log('[Asaas Payment] Exceção ao criar cliente: ' . $e->getMessage());
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'Erro ao criar cliente no Asaas: ' . $e->getMessage()
                    ]);
                    return;
                }
            } else {
                error_log('[Asaas Payment] Cliente existente: ' . $customer_id);
            }
            
            // Processar pagamento conforme o tipo
            if(isset($post['payment_type']) && $post['payment_type'] == 'CREDIT_CARD') {
                // Validar campos obrigatórios do cartão
                if(empty($post['holderName']) || empty($post['cardNumber']) ||
                   empty($post['expiry']) || empty($post['ccv']) ||
                   (empty($post['holderCpf']) && empty($user_info['cpf']))) {
                    error_log('[Asaas Payment] Erro: Dados do cartão incompletos');
                    echo json_encode(['status' => 'error', 'message' => 'Por favor, preencha todos os campos do cartão de crédito']);
                    return;
                }

                // Validar CEP obrigatório para cartão de crédito
                if(empty($user_info['postal_code'])) {
                    error_log('[Asaas Payment] Erro: CEP não informado para cartão de crédito');
                    echo json_encode(['status' => 'error', 'message' => 'Informe o CEP do titular do cartão.']);
                    return;
                }

                // Validar formato do CEP
                $clean_postal_code = preg_replace('/[^0-9]/', '', $user_info['postal_code']);
                if(strlen($clean_postal_code) != 8) {
                    error_log('[Asaas Payment] Erro: CEP com formato inválido: ' . $user_info['postal_code']);
                    echo json_encode(['status' => 'error', 'message' => 'CEP inválido. Informe um CEP válido com 8 dígitos.']);
                    return;
                }

                // Validar número do endereço obrigatório para cartão de crédito
                $address_number = $this->input->post('address_number');
                if(empty($address_number) || trim($address_number) === '') {
                    error_log('[Asaas Payment] Erro: Número do endereço não informado');
                    echo json_encode(['status' => 'error', 'message' => 'Informe o número do endereço do titular do cartão.']);
                    return;
                }
                
                // Processar data de expiração (MM/AA)
                $expiry = explode('/', $post['expiry']);
                if(count($expiry) != 2) {
                    error_log('[Asaas Payment] Erro: Formato de data de expiração inválido: ' . $post['expiry']);
                    echo json_encode(['status' => 'error', 'message' => 'Formato de data de expiração inválido. Use MM/AA']);
                    return;
                }
                $expiryMonth = trim($expiry[0]);
                $expiryYear = trim($expiry[1]);
                
                // Adicionar 20 ao ano se for apenas 2 dígitos
                if(strlen($expiryYear) == 2) {
                    $expiryYear = '20' . $expiryYear;
                }
                
                // Obter CPF do titular - prioridade: 1) formulário 2) banco de dados
                $holder_cpf = '';
                if(!empty($post['holderCpf'])) {
                    $holder_cpf = preg_replace('/[^0-9]/', '', $post['holderCpf']);
                } elseif(!empty($user_info['cpf'])) {
                    $holder_cpf = preg_replace('/[^0-9]/', '', $user_info['cpf']);
                }
                
                if(empty($holder_cpf) || strlen($holder_cpf) != 11) {
                    error_log('[Asaas Payment] Erro: CPF do titular inválido: ' . $holder_cpf);
                    echo json_encode(['status' => 'error', 'message' => 'Por favor, informe um CPF válido para o titular do cartão']);
                    return;
                }
                
                $credit_card_data = [
                    'customer' => $customer_id,
                    'billingType' => 'CREDIT_CARD',
                    'value' => floatval($post['amount']),
                    'dueDate' => date('Y-m-d'),
                    'description' => 'Pedido #' . ($post['order_id'] ?? 'N/A'),
                    'externalReference' => $post['order_id'] ?? '',
                    'creditCard' => [
                        'holderName' => $post['holderName'],
                        'number' => preg_replace('/[^0-9]/', '', $post['cardNumber']),
                        'expiryMonth' => $expiryMonth,
                        'expiryYear' => $expiryYear,
                        'ccv' => $post['ccv']
                    ],
                    'creditCardHolderInfo' => [
                        'name' => $user_info['name'],
                        'email' => $user_info['email'],
                        'cpfCnpj' => $holder_cpf,
                        'postalCode' => preg_replace('/[^0-9]/', '', $user_info['postal_code'] ?? ''),
                        'address' => $user_info['address'] ?? '',
                        'addressNumber' => $this->input->post('address_number') ?: '1',
                        'phone' => $phone
                    ]
                ];
                error_log('[Asaas Payment] Iniciando pagamento com cartão');
                error_log('[Asaas Payment] Dados: ' . json_encode($credit_card_data));
                
                $payment = $asaasClient->createPayment($credit_card_data);
                
                if($payment && isset($payment->id)) {
                    error_log('[Asaas Payment] Pagamento criado com sucesso: ' . $payment->id);
                    echo json_encode([
                        'status' => 'success',
                        'payment_id' => $payment->id,
                        'redirect_url' => base_url("payment/asaas_success?payment_id={$payment->id}&order_id={$post['order_id']}")
                    ]);
                } else {
                    $error_msg = $asaasClient->getLastError();
                    error_log('[Asaas Payment] Erro ao criar pagamento: ' . $error_msg);
                    echo json_encode([
                        'status' => 'error',
                        'message' => $error_msg
                    ]);
                }
            } elseif(isset($post['payment_type']) && $post['payment_type'] == 'PIX') {
                $prazo_pix = isset($asaas->asaas_prazo_pix) ? $asaas->asaas_prazo_pix : 1;
                $due_date = date('Y-m-d', strtotime("+{$prazo_pix} days"));
                $pix_data = [
                    'customer' => $customer_id,
                    'billingType' => 'PIX',
                    'value' => floatval($post['amount']),
                    'dueDate' => $due_date,
                    'description' => 'Pedido #' . ($post['order_id'] ?? 'N/A'),
                    'externalReference' => $post['order_id'] ?? ''
                ];
                error_log('[Asaas Payment] Iniciando pagamento com PIX');
                error_log('[Asaas Payment] Dados PIX: ' . json_encode($pix_data));
                
                $payment = $asaasClient->createPayment($pix_data);
                
                if($payment && isset($payment->id)) {
                    $pix_info = $asaasClient->getPixQrCode($payment->id);
                    
                    if($pix_info && isset($pix_info->payload)) {
                        error_log('[Asaas Payment] PIX gerado com sucesso: ' . $payment->id);
                        echo json_encode([
                            'status' => 'success',
                            'payment_id' => $payment->id,
                            'qrcode' => 'data:image/png;base64,' . $pix_info->encodedImage,
                            'qrcode_copy_paste' => $pix_info->payload
                        ]);
                    } else {
                        error_log('[Asaas Payment] Erro ao gerar QR Code PIX: ' . $asaasClient->getLastError());
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'Erro ao gerar QR Code PIX'
                        ]);
                    }
                } else {
                    $error_msg = $asaasClient->getLastError();
                    error_log('[Asaas Payment] Erro ao criar pagamento PIX: ' . $error_msg);
                    echo json_encode([
                        'status' => 'error',
                        'message' => $error_msg
                    ]);
                }
            } else {
                error_log('[Asaas Payment] Método de pagamento não suportado: ' . ($post['payment_type'] ?? 'não informado'));
                echo json_encode(['status' => 'error', 'message' => 'Método de pagamento não suportado']);
            }
        } catch (Exception $e) {
            error_log('[Asaas Payment] Exceção: ' . $e->getMessage());
            echo json_encode(['status' => 'error', 'message' => 'Erro no processamento: ' . $e->getMessage()]);
        } catch (Throwable $e) {
            error_log('[Asaas Payment] Erro fatal: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            echo json_encode(['status' => 'error', 'message' => 'Erro fatal: ' . $e->getMessage()]);
        }
    }
    
    public function create_asaas_subscription()
    {
        // Log para debug
        error_log('[Asaas Subscription] Método create_asaas_subscription chamado');
        error_log('[Asaas Subscription] POST data: ' . json_encode($this->input->post()));
        error_log('[Asaas Subscription] GET data: ' . json_encode($this->input->get()));

        $this->security->csrf_verify = false;
        $post = $this->input->post();

        if(empty($post)) {
            error_log('[Asaas Subscription] Erro: Dados POST vazios');
            echo json_encode(['status' => 'error', 'message' => 'Nenhum dado POST recebido']);
            return;
        }

        if(empty($post['slug']) || empty($post['account_slug'])) {
            error_log('[Asaas Subscription] Erro: slug ou account_slug ausentes');
            echo json_encode(['status' => 'error', 'message' => 'Dados inválidos: slug ou account_slug ausentes']);
            return;
        }
        
        $settings = settings();
        $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
        $apiKey = isset($asaas->asaas_api_key) ? $asaas->asaas_api_key : '';
        $baseUrl = isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1 ? 'https://www.asaas.com/api/v3' : 'https://sandbox.asaas.com/api/v3';

        error_log('[Asaas Subscription] API Key configurada: ' . (!empty($apiKey) ? 'Sim' : 'Não'));

        if(empty($apiKey)) {
            error_log('[Asaas Subscription] Erro: API Key não configurada');
            echo json_encode(['status' => 'error', 'message' => 'Asaas não configurado - API Key ausente']);
            return;
        }

        // Verificar se o AsaasGuzzleClient existe
        $client_path = APPPATH . '../asaas_scripts/AsaasGuzzleClient.php';
        if (!file_exists($client_path)) {
            error_log('[Asaas Subscription] Erro: AsaasGuzzleClient.php não encontrado em: ' . $client_path);
            echo json_encode(['status' => 'error', 'message' => 'Cliente Asaas não encontrado']);
            return;
        }
        
        // Obter informações do usuário e pacote
        $u_info = get_all_user_info_slug($post['slug']);
        $package = $this->admin_m->get_package_info_by_slug($post['account_slug']);
        
        if(empty($u_info) || empty($package)) {
            echo json_encode(['status' => 'error', 'message' => 'Usuário ou pacote não encontrado']);
            return;
        }
        
        // Verificar se cliente existe no Asaas ou criar novo
        $customer_id = $this->get_or_create_asaas_customer($u_info, $apiKey, $baseUrl);
        
        if(!$customer_id) {
            echo json_encode(['status' => 'error', 'message' => 'Erro ao criar cliente no Asaas']);
            return;
        }
        
        // Configurar dados da assinatura
        $subscription_data = [
            'customer' => $customer_id,
            'billingType' => $post['subscription_type'],
            'value' => $package['price'],
            'nextDueDate' => date('Y-m-d'),
            'description' => 'Assinatura ' . $package['package_name'],
            'cycle' => 'MONTHLY'
        ];
        
        // Adicionar dados do cartão se for pagamento recorrente por cartão
        if($post['subscription_type'] == 'CREDIT_CARD') {
            $subscription_data['creditCard'] = [
                'holderName' => $post['holder_name'],
                'number' => $post['card_number'],
                'expiryMonth' => $post['expiry_month'],
                'expiryYear' => $post['expiry_year'],
                'ccv' => $post['cvv']
            ];
            
            $subscription_data['creditCardHolderInfo'] = [
                'name' => $post['holder_name'],
                'email' => $u_info['email'],
                'cpfCnpj' => $post['holder_cpf'],
                'postalCode' => $post['holder_postal_code'],
                'addressNumber' => $post['holder_address_number'],
                'phone' => $post['holder_phone']
            ];
        }
        
        // Criar assinatura no Asaas
        $subscription = $this->create_asaas_api_subscription($subscription_data, $apiKey, $baseUrl);
        
        if(isset($subscription->id)) {
            // Assinatura criada com sucesso
            
            // Atualizar dados do usuário no sistema
            $payment_data = array(
                'user_id' => $u_info['user_id'],
                'account_type' => $package['id'],
                'price' => $package['price'],
                'currency_code' => get_currency('currency_code'),
                'status' => 'active',
                'txn_id' => $subscription->id,
                'payment_type' => 'asaas',
                'all_info' => json_encode($subscription),
                'created_at' => d_time(),
                'expire_date' => add_year($package['package_type'], $package['duration'], $package['duration_period']),
                'is_running' => 1,
                'is_self' => 1,
            );
            
            $insert = $this->common_m->insert($payment_data, 'payment_info');
            
            if($insert) {
                $this->common_m->update(array(
                    'is_payment' => 1,
                    'is_expired' => 0,
                    'is_request' => 0,
                    'start_date' => d_time(),
                    'end_date' => add_year($package['package_type'], $package['duration'], $package['duration_period']),
                    'account_type' => $package['id']
                ), $u_info['user_id'], 'users');
                
                echo json_encode([
                    'status' => 'success',
                    'subscription_id' => $subscription->id,
                    'redirect_url' => base_url("payment/successMsg/{$u_info['username']}")
                ]);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Erro ao salvar dados de pagamento']);
            }
        } else {
            // Erro ao criar assinatura
            echo json_encode([
                'status' => 'error',
                'message' => isset($subscription->errors[0]->description) ? $subscription->errors[0]->description : 'Erro ao criar assinatura'
            ]);
        }
    }

    public function check_asaas_payment_status()
    {
        $this->security->csrf_verify = false;
        $payment_id = $this->input->post('payment_id');
        
        if(empty($payment_id)) {
            echo json_encode(['status' => 'error', 'message' => 'ID de pagamento não fornecido']);
            return;
        }
        
        // Adicionar log para depuração
        error_log('[Asaas Status] Verificando status do pagamento: ' . $payment_id);
        
        $settings = settings();
        $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
        $apiKey = isset($asaas->asaas_api_key) ? $asaas->asaas_api_key : '';
        $sandbox = !(isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1);
        
        // Carregar nossa classe cliente
        require_once FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';
        
        // Inicializar o cliente Asaas
        $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);
        
        if (!$asaasClient->isReady()) {
            error_log('[Asaas Status] Erro: Cliente Asaas não inicializado: ' . $asaasClient->getLastError());
            echo json_encode(['status' => 'error', 'message' => 'Erro ao inicializar conexão com Asaas']);
            return;
        }
        
        try {
            // Obter informações do pagamento
            $result = $asaasClient->getPayment($payment_id);
            
            if(!$result || !isset($result->status)) {
                error_log('[Asaas Status] Erro ao obter informações do pagamento: ' . $asaasClient->getLastError());
                echo json_encode(['status' => 'error', 'message' => 'Erro ao obter informações do pagamento']);
                return;
            }
            
            error_log('[Asaas Status] Status do pagamento: ' . $result->status);
            
            if($result->status == 'CONFIRMED' || $result->status == 'RECEIVED') {
                // Pagamento confirmado
                $order_id = $result->externalReference;
                error_log('[Asaas Status] Pagamento confirmado para o pedido: ' . $order_id);
                
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Pagamento confirmado',
                    'redirect_url' => base_url("payment/asaas_success?payment_id={$payment_id}&order_id={$order_id}")
                ]);
        } else {
                // Pagamento ainda não confirmado
                error_log('[Asaas Status] Pagamento ainda não confirmado. Status atual: ' . $result->status);
                echo json_encode(['status' => 'pending', 'message' => 'Aguardando pagamento']);
            }
        } catch(Exception $e) {
            error_log('[Asaas Status] Exceção: ' . $e->getMessage());
            echo json_encode(['status' => 'error', 'message' => 'Erro ao verificar status do pagamento']);
        }
    }
    
    public function asaas_success()
    {
        $get = $this->input->get();

        if(empty($get['payment_id'])) {
            $this->session->set_flashdata('error', 'ID de pagamento não fornecido');
            redirect(base_url());
            return;
        }

        error_log('[Asaas Success] Verificando pagamento: ' . $get['payment_id']);

        $settings = settings();
        $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
        $apiKey = isset($asaas->asaas_api_key) ? $asaas->asaas_api_key : '';
        $sandbox = !(isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1);

        // Carregar nossa classe cliente
        require_once FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';

        // Inicializar o cliente Asaas
        $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);

        if (!$asaasClient->isReady()) {
            error_log('[Asaas Success] Erro: Cliente Asaas não inicializado: ' . $asaasClient->getLastError());
            $this->session->set_flashdata('error', 'Erro ao inicializar conexão com Asaas');
            redirect(base_url());
            return;
        }

        // Obter informações do pagamento
        $payment_info = $asaasClient->getPayment($get['payment_id']);

        if(!$payment_info) {
            error_log('[Asaas Success] Erro ao obter informações do pagamento: ' . $asaasClient->getLastError());
            $this->session->set_flashdata('error', 'Erro ao obter informações do pagamento');
            redirect(base_url());
            return;
        }

        error_log('[Asaas Success] Status do pagamento: ' . $payment_info->status);

        if(isset($payment_info->status) && ($payment_info->status == 'RECEIVED' || $payment_info->status == 'CONFIRMED')) {
            // Obter informações do pedido
            $order_id = $payment_info->externalReference;
            error_log('[Asaas Success] Ordem de referência: ' . $order_id);

            // Primeiro, verificar se o pedido existe na sessão (caso ainda não tenha sido salvo)
            $session_payment = $this->session->userdata('payment');
            $order_data = null;

            // Tentar obter o pedido do banco de dados primeiro
            $order_data = $this->common_m->get_order_by_id($order_id);

            // Se não encontrou no banco, mas existe na sessão, criar o pedido agora
            if(!$order_data && $session_payment && $session_payment['uid'] == $order_id) {
                error_log('[Asaas Success] Pedido não encontrado no banco, criando a partir da sessão: ' . $order_id);

                // Carregar modelos necessários
                $this->load->model(['order_m', 'cart_m']);

                // Criar o pedido a partir dos dados da sessão
                $insert = $this->order_m->create_order($session_payment);

                if($insert) {
                    // Inserir itens do pedido se existirem no carrinho
                    $this->order_m->order_info($insert, $session_payment, 0);

                    // Obter os dados do pedido recém-criado
                    $order_data = $this->common_m->get_order_by_id($order_id);
                    error_log('[Asaas Success] Pedido criado com sucesso: ' . $order_id);
                } else {
                    error_log('[Asaas Success] Erro ao criar pedido: ' . $order_id);
                }
            }

            // Se ainda não encontrou o pedido, tentar buscar pelo UID
            if(!$order_data) {
                $this->db->select('*');
                $this->db->from('order_user_list');
                $this->db->where('uid', $order_id);
                $query = $this->db->get();
                if ($query->num_rows() > 0) {
                    $order_data = $query->row_array();
                    error_log('[Asaas Success] Pedido encontrado pelo UID: ' . $order_id);
                }
            }

            if($order_data) {
                // Verificar se o pagamento já foi registrado
                $payment = $this->common_m->get_payment_info_by_order_id($order_id);

                if(!$payment) {
                    error_log('[Asaas Success] Registrando pagamento para o pedido: ' . $order_id);

                    // Salvar informações de pagamento (seguindo padrão dos outros métodos)
                    $payment_data = [
                        'user_id' => $order_data['user_id'] ?? 0,
                        'order_id' => $order_id,
                        'shop_id' => $order_data['shop_id'],
                        'order_type' => $order_data['order_type'] ?? 1,
                        'price' => $payment_info->value,
                        'currency_code' => 'BRL',
                        'status' => 'success',
                        'txn_id' => $payment_info->id,
                        'payment_by' => 'asaas',
                        'created_at' => d_time(),
                        'all_info' => json_encode($payment_info),
                    ];

                    $this->common_m->insert($payment_data, 'order_payment_info');
                    error_log('[Asaas Success] Pagamento registrado com sucesso');
                }

                // Obter informações do restaurante para o redirect
                $shop_info = $this->common_m->get_restaurant_info_by_id($order_data['shop_id']);

                if($shop_info) {
                    // Seguir o mesmo padrão dos outros métodos de pagamento
                    // Redirecionar para payment_success que irá atualizar o status do pedido
                    $success_url = base_url('user_payment/payment_success/' . $shop_info['username'] . '?txn_id=' . $payment_info->id . '&method=asaas&amount=' . $payment_info->value);

                    // Adicionar parâmetro de idioma se disponível
                    if(isset($_GET['lang'])) {
                        $success_url .= '&lang=' . $_GET['lang'];
                    }

                    error_log('[Asaas Success] Redirecionando para payment_success: ' . $success_url);
                    $this->session->set_flashdata('success_msg', 'Pagamento confirmado com sucesso!');
                    redirect($success_url);
                } else {
                    error_log('[Asaas Success] Informações do restaurante não encontradas para shop_id: ' . $order_data['shop_id']);
                    $this->session->set_flashdata('error', 'Erro ao obter informações do restaurante');
                    redirect(base_url());
                }
            } else {
                error_log('[Asaas Success] Pedido não encontrado e não foi possível criar: ' . $order_id);
                $this->session->set_flashdata('error', 'Pedido não encontrado');
                redirect(base_url());
            }
        } else {
            error_log('[Asaas Success] Pagamento não confirmado. Status: ' . $payment_info->status);
            $this->session->set_flashdata('error', 'Pagamento não confirmado');
            redirect(base_url());
        }
    }
    
    public function asaas_webhook()
    {
        // Log the webhook call with domain info
        $current_domain = $_SERVER['HTTP_HOST'] ?? 'unknown';
        error_log('[Asaas Webhook] Received webhook call on domain: ' . $current_domain);

        // Get the raw POST data
        $raw_post_data = file_get_contents('php://input');

        // Log the raw data for debugging
        error_log('[Asaas Webhook] Raw data: ' . $raw_post_data);
        file_put_contents(APPPATH.'logs/asaas_webhook_'.date('Y-m-d').'.log', date('Y-m-d H:i:s').' ['.$current_domain.']: '.$raw_post_data.PHP_EOL, FILE_APPEND);

        // Decode the JSON
        $webhook_data = json_decode($raw_post_data, true);

        if(!$webhook_data) {
            error_log('[Asaas Webhook] Invalid JSON data received');
            http_response_code(400);
            echo json_encode(['status' => 'error', 'message' => 'Invalid JSON data']);
            return;
        }

        // Log the event data
        error_log('[Asaas Webhook] Event data: ' . print_r($webhook_data, true));

        // Extract tenant information from payment data
        $tenant_info = $this->_identify_tenant_from_webhook($webhook_data);

        if (!$tenant_info) {
            error_log('[Asaas Webhook] Could not identify tenant from webhook data');
            http_response_code(400);
            echo json_encode(['status' => 'error', 'message' => 'Could not identify tenant']);
            return;
        }

        error_log('[Asaas Webhook] Processing for tenant: ' . json_encode($tenant_info));

        // Process based on event type with tenant context
        if (isset($webhook_data['event'])) {
            switch ($webhook_data['event']) {
                case 'PAYMENT_CONFIRMED':
                    $this->_process_confirmed_payment($webhook_data['payment'], $tenant_info);
                    break;
                case 'PAYMENT_RECEIVED':
                    $this->_process_received_payment($webhook_data['payment'], $tenant_info);
                    break;
                case 'PAYMENT_OVERDUE':
                    $this->_process_overdue_payment($webhook_data['payment'], $tenant_info);
                    break;
                case 'SUBSCRIPTION_RECEIVED':
                case 'SUBSCRIPTION_RENEWED':
                    $this->_process_subscription_renewal($webhook_data, $tenant_info);
                    break;
                case 'SUBSCRIPTION_OVERDUE':
                    $this->_process_subscription_overdue($webhook_data, $tenant_info);
                    break;
                case 'SUBSCRIPTION_CANCELED':
                    $this->_process_subscription_canceled($webhook_data, $tenant_info);
                    break;
                default:
                    error_log('[Asaas Webhook] Unknown event type: ' . $webhook_data['event']);
            }
        } else {
            error_log('[Asaas Webhook] No event type specified in webhook data');
        }

        // Return 200 OK to Asaas
        http_response_code(200);
        echo json_encode(['status' => 'success']);
        exit;
    }

    /**
     * Identify tenant from webhook data
     * Uses external reference or customer data to determine which tenant this payment belongs to
     */
    private function _identify_tenant_from_webhook($webhook_data)
    {
        $tenant_info = null;

        // Try to get tenant info from payment data
        if (isset($webhook_data['payment'])) {
            $payment = $webhook_data['payment'];

            // Method 1: Extract from external reference (order ID format: tenant_orderid or just orderid)
            if (isset($payment['externalReference']) && !empty($payment['externalReference'])) {
                $external_ref = $payment['externalReference'];
                error_log('[Asaas Webhook] External reference: ' . $external_ref);

                // Check if external reference contains tenant info (format: tenant_orderid)
                if (strpos($external_ref, '_') !== false) {
                    $parts = explode('_', $external_ref, 2);
                    $potential_tenant = $parts[0];
                    $order_id = $parts[1];

                    // Verify if this is a valid tenant
                    $tenant_info = $this->_get_tenant_by_identifier($potential_tenant);
                    if ($tenant_info) {
                        $tenant_info['order_id'] = $order_id;
                        error_log('[Asaas Webhook] Tenant identified from external reference: ' . $potential_tenant);
                        return $tenant_info;
                    }
                }

                // Method 2: Look up order in database to find tenant
                if (is_numeric($external_ref)) {
                    $tenant_info = $this->_get_tenant_by_order_id($external_ref);
                    if ($tenant_info) {
                        $tenant_info['order_id'] = $external_ref;
                        error_log('[Asaas Webhook] Tenant identified from order lookup: ' . $tenant_info['username']);
                        return $tenant_info;
                    }
                }
            }

            // Method 3: Extract from customer external reference
            if (isset($payment['customer']) && !empty($payment['customer'])) {
                $customer_id = $payment['customer'];
                $tenant_info = $this->_get_tenant_by_customer_id($customer_id);
                if ($tenant_info) {
                    error_log('[Asaas Webhook] Tenant identified from customer ID: ' . $tenant_info['username']);
                    return $tenant_info;
                }
            }
        }

        // Method 4: Try subscription data
        if (isset($webhook_data['subscription'])) {
            $subscription = $webhook_data['subscription'];
            if (isset($subscription['customer'])) {
                $customer_id = $subscription['customer'];
                $tenant_info = $this->_get_tenant_by_customer_id($customer_id);
                if ($tenant_info) {
                    error_log('[Asaas Webhook] Tenant identified from subscription customer: ' . $tenant_info['username']);
                    return $tenant_info;
                }
            }
        }

        error_log('[Asaas Webhook] Could not identify tenant from webhook data');
        return null;
    }

    /**
     * Get tenant by identifier (username or subdomain)
     */
    private function _get_tenant_by_identifier($identifier)
    {
        // First try to find by username
        $this->db->where('username', $identifier);
        $user = $this->db->get('users')->row_array();

        if ($user) {
            return $user;
        }

        // Try to find by custom domain/subdomain
        $this->db->where('request_name', $identifier);
        $this->db->where('status', 2);
        $this->db->where('is_ready', 1);
        $domain = $this->db->get('custom_domain_list')->row_array();

        if ($domain) {
            $this->db->where('id', $domain['user_id']);
            $user = $this->db->get('users')->row_array();
            return $user;
        }

        return null;
    }

    /**
     * Get tenant by order ID
     */
    private function _get_tenant_by_order_id($order_id)
    {
        $this->db->select('u.*');
        $this->db->from('order_user_list o');
        $this->db->join('users u', 'u.id = o.shop_id', 'left');
        $this->db->where('o.id', $order_id);
        $result = $this->db->get()->row_array();

        return $result;
    }

    /**
     * Get tenant by Asaas customer ID
     */
    private function _get_tenant_by_customer_id($customer_id)
    {
        // Look for customer ID in user metadata or payment records
        $this->db->where('asaas_customer_id', $customer_id);
        $user = $this->db->get('users')->row_array();

        if ($user) {
            return $user;
        }

        // Alternative: look in payment records
        $this->db->select('u.*');
        $this->db->from('payment_info p');
        $this->db->join('users u', 'u.id = p.user_id', 'left');
        $this->db->where('p.all_info LIKE', '%"customer":"' . $customer_id . '"%');
        $this->db->limit(1);
        $result = $this->db->get()->row_array();

        return $result;
    }

    private function _process_confirmed_payment($payment, $tenant_info = null)
    {
        // Extract external reference (your order ID)
        $externalReference = isset($payment['externalReference']) ? $payment['externalReference'] : null;
        $order_id = isset($tenant_info['order_id']) ? $tenant_info['order_id'] : $externalReference;

        if ($order_id) {
            // Update order status in your database
            error_log('[Asaas Webhook] Payment confirmed for order: ' . $order_id .
                     ($tenant_info ? ' (tenant: ' . $tenant_info['username'] . ')' : ''));

            // Check if it's a numeric order ID (regular order) or subscription
            if (is_numeric($order_id)) {
                // Additional validation: ensure order belongs to the identified tenant
                if ($tenant_info) {
                    $this->db->where('id', $order_id);
                    $this->db->where('shop_id', $tenant_info['id']);
                    $order_exists = $this->db->get('order_user_list')->num_rows();

                    if ($order_exists == 0) {
                        error_log('[Asaas Webhook] Order ' . $order_id . ' does not belong to tenant ' . $tenant_info['username']);
                        return;
                    }
                }

                // Update order status
                $this->db->where('id', $order_id);
                if ($tenant_info) {
                    $this->db->where('shop_id', $tenant_info['id']); // Additional security check
                }
                $this->db->update('order_user_list', [
                    'status' => 2, // 2 = completed
                    'is_payment' => 1,
                    'payment_by' => 'asaas'
                ]);

                error_log('[Asaas Webhook] Order ' . $order_id . ' marked as paid');
            }
        }
    }

    private function _process_received_payment($payment, $tenant_info = null)
    {
        // Similar to _process_confirmed_payment
        $this->_process_confirmed_payment($payment, $tenant_info);
    }

    private function _process_overdue_payment($payment, $tenant_info = null)
    {
        $externalReference = isset($payment['externalReference']) ? $payment['externalReference'] : null;
        $order_id = isset($tenant_info['order_id']) ? $tenant_info['order_id'] : $externalReference;

        if ($order_id && is_numeric($order_id)) {
            // Additional validation: ensure order belongs to the identified tenant
            if ($tenant_info) {
                $this->db->where('id', $order_id);
                $this->db->where('shop_id', $tenant_info['id']);
                $order_exists = $this->db->get('order_user_list')->num_rows();

                if ($order_exists == 0) {
                    error_log('[Asaas Webhook] Order ' . $order_id . ' does not belong to tenant ' . $tenant_info['username']);
                    return;
                }
            }

            // Update order status to overdue
            $this->db->where('id', $order_id);
            if ($tenant_info) {
                $this->db->where('shop_id', $tenant_info['id']); // Additional security check
            }
            $this->db->update('order_user_list', [
                'payment_status' => 'overdue',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            error_log('[Asaas Webhook] Order ' . $order_id . ' marked as overdue' .
                     ($tenant_info ? ' (tenant: ' . $tenant_info['username'] . ')' : ''));
        }
    }

    private function _process_subscription_renewal($webhook_data, $tenant_info = null)
    {
        // Handle subscription renewal
        error_log('[Asaas Webhook] Processing subscription renewal' .
                 ($tenant_info ? ' for tenant: ' . $tenant_info['username'] : ''));

        if ($tenant_info && isset($webhook_data['subscription'])) {
            $subscription = $webhook_data['subscription'];

            // Update user subscription status
            $this->db->where('user_id', $tenant_info['id']);
            $this->db->where('txn_id', $subscription['id']);
            $this->db->update('payment_info', [
                'status' => 'active',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            error_log('[Asaas Webhook] Subscription renewed for tenant: ' . $tenant_info['username']);
        }
    }

    private function _process_subscription_overdue($webhook_data, $tenant_info = null)
    {
        // Handle subscription overdue
        error_log('[Asaas Webhook] Processing subscription overdue' .
                 ($tenant_info ? ' for tenant: ' . $tenant_info['username'] : ''));

        if ($tenant_info && isset($webhook_data['subscription'])) {
            $subscription = $webhook_data['subscription'];

            // Update user subscription status
            $this->db->where('user_id', $tenant_info['id']);
            $this->db->where('txn_id', $subscription['id']);
            $this->db->update('payment_info', [
                'status' => 'overdue',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            error_log('[Asaas Webhook] Subscription marked overdue for tenant: ' . $tenant_info['username']);
        }
    }

    private function _process_subscription_canceled($webhook_data, $tenant_info = null)
    {
        // Handle subscription cancellation
        error_log('[Asaas Webhook] Processing subscription cancellation' .
                 ($tenant_info ? ' for tenant: ' . $tenant_info['username'] : ''));

        if ($tenant_info && isset($webhook_data['subscription'])) {
            $subscription = $webhook_data['subscription'];

            // Update user subscription status
            $this->db->where('user_id', $tenant_info['id']);
            $this->db->where('txn_id', $subscription['id']);
            $this->db->update('payment_info', [
                'status' => 'canceled',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // Also update user account status
            $this->db->where('id', $tenant_info['id']);
            $this->db->update('users', [
                'is_payment' => 0,
                'is_expired' => 1
            ]);

            error_log('[Asaas Webhook] Subscription canceled for tenant: ' . $tenant_info['username']);
        }
    }

    /* Métodos auxiliares para API do Asaas */
    
    private function get_or_create_asaas_customer($user_info, $apiKey, $baseUrl)
    {
        // Verificar se o GuzzleHttp está disponível
        if (!class_exists('GuzzleHttp\Client')) {
            error_log('[Asaas Customer] Erro: GuzzleHttp\Client não encontrado');
            throw new Exception('GuzzleHttp\Client não encontrado');
        }
        
        // Criar cliente HTTP
        $client = new \GuzzleHttp\Client();
        
        // Verificar se usuário já tem customer_id do Asaas
        $existing_id = null;
        
        // Se temos uma função para obter o ID do Asaas, usamos ela
        if(method_exists($this, 'get_asaas_customer_id') && isset($user_info['user_id'])) {
            $existing_id = $this->get_asaas_customer_id($user_info['user_id']);
            error_log('[Asaas Customer] Verificando ID do cliente para user_id '.$user_info['user_id'].': '.($existing_id ? $existing_id : 'não encontrado'));
        } else {
            error_log('[Asaas Customer] Não foi possível verificar ID existente - user_id não disponível ou método não existe');
        }
        
        if($existing_id) {
            // Verificar se o cliente existe no Asaas
            try {
                error_log('[Asaas Customer] Verificando se o cliente '.$existing_id.' existe no Asaas');
                $response = $client->request('GET', $baseUrl . '/customers/'.$existing_id, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'access_token' => $apiKey
                    ]
                ]);
                
                $result = json_decode($response->getBody()->getContents());
                
                if(isset($result->id)) {
                    error_log('[Asaas Customer] Cliente verificado e válido no Asaas: '.$existing_id);
                    return $existing_id;
                }
            } catch(Exception $e) {
                error_log('[Asaas Customer] Erro ao verificar cliente existente: '.$e->getMessage());
                // Se o cliente não existir, continuamos para criar um novo
            }
        }
        
        // Extrair e validar dados necessários para criar o cliente
        $email = !empty($user_info['email']) ? $user_info['email'] : 'cliente_'.time().'@temp.com';
        
        // Limpar e validar CPF/CNPJ
        $cpf = !empty($user_info['cpf']) ? preg_replace('/[^0-9]/', '', $user_info['cpf']) : '';
        if (empty($cpf) && !empty($user_info['document'])) {
            $cpf = preg_replace('/[^0-9]/', '', $user_info['document']);
        }
        
        // Gerar CPF aleatório se não tiver (apenas para teste no sandbox)
        if (empty($cpf) || strlen($cpf) < 11) {
            $cpf = '12345678909'; // CPF genérico para testes
            error_log('[Asaas Customer] Usando CPF genérico para teste: '.$cpf);
        }
        
        // Nome do cliente
        $name = !empty($user_info['name']) ? $user_info['name'] : (!empty($user_info['username']) ? $user_info['username'] : 'Cliente '.substr(md5(time()), 0, 8));
        
        // Garantir que o telefone esteja formatado corretamente
        $phone = '';
        if(!empty($user_info['phone'])) {
            $phone = preg_replace('/[^0-9]/', '', $user_info['phone']);
            
            // Verificar o comprimento do telefone
            if(strlen($phone) < 10) {
                error_log('[Asaas Customer] Telefone com menos de 10 dígitos: ' . $phone);
                
                // Adicionar DDD padrão se tiver apenas o número local
                if(strlen($phone) == 8 || strlen($phone) == 9) {
                    $phone = '11' . $phone; // DDD padrão 11 (São Paulo)
                    error_log('[Asaas Customer] Adicionando DDD padrão. Telefone ajustado: ' . $phone);
                }
            }
        }
        
        // Se ainda não tiver telefone válido, usar um valor padrão
        if(empty($phone) || strlen($phone) < 10) {
            $phone = '11999999999'; // Valor padrão para telefone
            error_log('[Asaas Customer] Usando telefone padrão: ' . $phone);
        }
        
        // Referência externa (ID do usuário ou valor temporário)
        $external_ref = isset($user_info['user_id']) ? 'user_'.$user_info['user_id'] : 'temp_'.time().'_'.rand(1000, 9999);
        
        // Construir dados do cliente para a API
        $customer_data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'cpfCnpj' => $cpf,
            'externalReference' => $external_ref
        ];
        
        error_log('[Asaas Customer] Tentando criar cliente com dados: ' . json_encode($customer_data));
        
        // Tentativa 1: Buscar cliente por CPF
        try {
            error_log('[Asaas Customer] Verificando se já existe cliente com CPF: ' . $cpf);
            $response = $client->request('GET', $baseUrl . '/customers?cpfCnpj=' . $cpf, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ]
            ]);
            
            $result = json_decode($response->getBody()->getContents());
            
            if(isset($result->data) && is_array($result->data) && count($result->data) > 0) {
                $customer_id = $result->data[0]->id;
                error_log('[Asaas Customer] Cliente encontrado por CPF: ' . $customer_id);
                
                // Atualizar dados do cliente para manter sincronizado
                try {
                    $client->request('POST', $baseUrl . '/customers/' . $customer_id, [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'access_token' => $apiKey
                        ],
                        'json' => [
                            'name' => $name,
                            'email' => $email,
                            'phone' => $phone
                        ]
                    ]);
                    error_log('[Asaas Customer] Dados do cliente atualizados com sucesso');
                } catch(Exception $updateErr) {
                    error_log('[Asaas Customer] Erro ao atualizar dados do cliente: ' . $updateErr->getMessage());
                }
                
                // Salvar o ID do cliente para o usuário
                if(method_exists($this, 'update_asaas_customer_id') && isset($user_info['user_id'])) {
                    $this->update_asaas_customer_id($user_info['user_id'], $customer_id);
                    error_log('[Asaas Customer] ID do cliente salvo para o usuário ' . $user_info['user_id']);
                }
                
                return $customer_id;
            }
        } catch(Exception $e) {
            error_log('[Asaas Customer] Erro ao buscar cliente por CPF: ' . $e->getMessage());
        }
        
        // Tentativa 2: Criar novo cliente
        try {
            error_log('[Asaas Customer] Criando novo cliente');
            $response = $client->request('POST', $baseUrl . '/customers', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ],
                'json' => $customer_data
            ]);
            
            $result = json_decode($response->getBody()->getContents());
            
            if(isset($result->id)) {
                $customer_id = $result->id;
                error_log('[Asaas Customer] Novo cliente criado com sucesso: ' . $customer_id);
                
                // Salvar o ID do cliente para o usuário
                if(method_exists($this, 'update_asaas_customer_id') && isset($user_info['user_id'])) {
                    $this->update_asaas_customer_id($user_info['user_id'], $customer_id);
                    error_log('[Asaas Customer] ID do cliente salvo para o usuário ' . $user_info['user_id']);
                }
                
                return $customer_id;
            } else {
                error_log('[Asaas Customer] Resposta sem ID de cliente: ' . json_encode($result));
            }
        } catch(Exception $e) {
            error_log('[Asaas Customer] Erro ao criar novo cliente: ' . $e->getMessage());
            
            // Verificar resposta para tratar erros específicos
            if ($e instanceof \GuzzleHttp\Exception\ClientException) {
                $response = $e->getResponse();
                $errorBody = json_decode($response->getBody()->getContents(), true);
                error_log('[Asaas Customer] Detalhes do erro: ' . json_encode($errorBody));
                
                // Verificar se é erro de CPF inválido e tentar com outro valor
                if (isset($errorBody['errors']) && is_array($errorBody['errors'])) {
                    foreach ($errorBody['errors'] as $error) {
                        if (isset($error['code']) && $error['code'] == 'invalid_cpfCnpj') {
                            // Tentar novamente com CPF alternativo
                            $cpf_alt = '11122233344'; // Outro CPF genérico
                            $customer_data['cpfCnpj'] = $cpf_alt;
                            
                            error_log('[Asaas Customer] Tentando novamente com CPF alternativo: ' . $cpf_alt);
                            try {
                                $response = $client->request('POST', $baseUrl . '/customers', [
                                    'headers' => [
                                        'Content-Type' => 'application/json',
                                        'access_token' => $apiKey
                                    ],
                                    'json' => $customer_data
                                ]);
                                
                                $result = json_decode($response->getBody()->getContents());
                                
                                if(isset($result->id)) {
                                    $customer_id = $result->id;
                                    error_log('[Asaas Customer] Cliente criado com CPF alternativo: ' . $customer_id);
                                    
                                    if(method_exists($this, 'update_asaas_customer_id') && isset($user_info['user_id'])) {
                                        $this->update_asaas_customer_id($user_info['user_id'], $customer_id);
                                    }
                                    
                                    return $customer_id;
                                }
                            } catch(Exception $e2) {
                                error_log('[Asaas Customer] Erro na segunda tentativa: ' . $e2->getMessage());
                            }
                        }
                    }
                }
            }
        }
        
        // Não foi possível criar ou encontrar cliente
        error_log('[Asaas Customer] Não foi possível criar ou encontrar cliente no Asaas');
        return null;
    }
    
    private function create_asaas_payment($payment_data, $apiKey, $baseUrl)
    {
        try {
            // Log de depuração para acompanhar os dados enviados
            error_log('[Asaas Payment] Enviando dados para API: ' . json_encode($payment_data));
            
            // Verificar se o GuzzleHttp está disponível
            if (!class_exists('GuzzleHttp\Client')) {
                error_log('[Asaas Payment] Erro: GuzzleHttp\Client não encontrado');
                throw new Exception('GuzzleHttp\Client não encontrado');
            }
            
            // Criar cliente HTTP
            $client = new \GuzzleHttp\Client();
            
            // Garantir que valores numéricos estejam como float
            if (isset($payment_data['value'])) {
                $payment_data['value'] = floatval($payment_data['value']);
            }
            
            // Garantir que CPF/CNPJ esteja formatado corretamente (apenas números)
            if (isset($payment_data['customerData']['cpfCnpj'])) {
                $payment_data['customerData']['cpfCnpj'] = preg_replace('/[^0-9]/', '', $payment_data['customerData']['cpfCnpj']);
            }
            
            if (isset($payment_data['creditCardHolderInfo']['cpfCnpj'])) {
                $payment_data['creditCardHolderInfo']['cpfCnpj'] = preg_replace('/[^0-9]/', '', $payment_data['creditCardHolderInfo']['cpfCnpj']);
            }
            
            // Garantir que o número do cartão esteja sem espaços
            if (isset($payment_data['creditCard']['number'])) {
                $payment_data['creditCard']['number'] = preg_replace('/[^0-9]/', '', $payment_data['creditCard']['number']);
            }
            
            // Fazer requisição para criar pagamento
            $response = $client->request('POST', $baseUrl . '/payments', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ],
                'json' => $payment_data
            ]);
            
            // Verificar status da resposta
            $statusCode = $response->getStatusCode();
            error_log('[Asaas Payment] Status da resposta: ' . $statusCode);
            
            if ($statusCode != 200) {
                error_log('[Asaas Payment] Erro: Status da resposta não é 200');
                return (object)['errors' => [(object)['description' => 'Status da resposta não é 200: ' . $statusCode]]];
            }
            
            // Obter corpo da resposta
            $body = $response->getBody()->getContents();
            error_log('[Asaas Payment] Resposta recebida com tamanho: ' . strlen($body));
            
            // Decodificar JSON
            $result = json_decode($body);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('[Asaas Payment] Erro ao decodificar JSON: ' . json_last_error_msg());
                return (object)['errors' => [(object)['description' => 'Erro ao decodificar JSON: ' . json_last_error_msg()]]];
            }
            
            error_log('[Asaas Payment] Pagamento criado com sucesso: ' . json_encode($result));
            return $result;
        } catch(\GuzzleHttp\Exception\ClientException $e) {
            // Capturar erros de cliente (400, 401, 403, etc.)
            $response = $e->getResponse();
            $errorBody = json_decode($response->getBody()->getContents());
            error_log('[Asaas Payment] Erro de API: ' . json_encode($errorBody));
            return $errorBody;
        } catch(\GuzzleHttp\Exception\RequestException $e) {
            error_log('[Asaas Payment] Erro de requisição: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $errorBody = json_decode($response->getBody()->getContents());
                error_log('[Asaas Payment] Corpo do erro: ' . json_encode($errorBody));
                return $errorBody;
            }
            
            return (object)['errors' => [(object)['description' => 'Erro de requisição: ' . $e->getMessage()]]];
        } catch(Exception $e) {
            error_log('[Asaas Payment] Erro ao criar pagamento no Asaas: ' . $e->getMessage());
            return (object)['errors' => [(object)['description' => $e->getMessage()]]];
        }
    }
    
    private function get_asaas_pix_info($payment_id, $apiKey, $baseUrl)
    {
        try {
            error_log('[Asaas PIX] Obtendo QR Code para pagamento ID: ' . $payment_id);
            
            // Verificar se o GuzzleHttp está disponível
            if (!class_exists('GuzzleHttp\Client')) {
                error_log('[Asaas PIX] Erro: GuzzleHttp\Client não encontrado');
                throw new Exception('GuzzleHttp\Client não encontrado');
            }
            
            // Criar cliente HTTP
            $client = new \GuzzleHttp\Client();
            
            // Fazer requisição para obter QR Code
            $response = $client->request('GET', $baseUrl . '/payments/' . $payment_id . '/pixQrCode', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ]
            ]);
            
            // Verificar status da resposta
            $statusCode = $response->getStatusCode();
            error_log('[Asaas PIX] Status da resposta: ' . $statusCode);
            
            if ($statusCode != 200) {
                error_log('[Asaas PIX] Erro: Status da resposta não é 200');
                return (object)['errors' => [(object)['description' => 'Status da resposta não é 200: ' . $statusCode]]];
            }
            
            // Obter corpo da resposta
            $body = $response->getBody()->getContents();
            error_log('[Asaas PIX] Resposta recebida com tamanho: ' . strlen($body));
            
            // Decodificar JSON
            $result = json_decode($body);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('[Asaas PIX] Erro ao decodificar JSON: ' . json_last_error_msg());
                return (object)['errors' => [(object)['description' => 'Erro ao decodificar JSON: ' . json_last_error_msg()]]];
            }
            
            // Verificar se os campos necessários estão presentes
            if (!isset($result->encodedImage) || !isset($result->payload)) {
                error_log('[Asaas PIX] Erro: Campos necessários não encontrados na resposta');
                error_log('[Asaas PIX] Resposta: ' . $body);
                return (object)['errors' => [(object)['description' => 'Campos necessários não encontrados na resposta']]];
            }
            
            error_log('[Asaas PIX] QR Code obtido com sucesso');
            return $result;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('[Asaas PIX] Erro de requisição: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $errorBody = json_decode($response->getBody()->getContents());
                error_log('[Asaas PIX] Corpo do erro: ' . json_encode($errorBody));
                return $errorBody;
            }
            
            return (object)['errors' => [(object)['description' => 'Erro de requisição: ' . $e->getMessage()]]];
        } catch (Exception $e) {
            error_log('[Asaas PIX] Exceção: ' . $e->getMessage());
            return (object)['errors' => [(object)['description' => 'Exceção: ' . $e->getMessage()]]];
        }
    }
    
    private function get_asaas_payment_info($payment_id, $apiKey, $baseUrl)
    {
        try {
            $response = $this->client->request('GET', $baseUrl . '/payments/' . $payment_id, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ]
            ]);
            
            return json_decode($response->getBody()->getContents());
        } catch(Exception $e) {
            log_message('error', 'Erro ao obter informações do pagamento: ' . $e->getMessage());
            return null;
        }
    }
    
    private function create_asaas_api_subscription($subscription_data, $apiKey, $baseUrl)
    {
        try {
            $response = $this->client->request('POST', $baseUrl . '/subscriptions', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ],
                'json' => $subscription_data
            ]);
            
            return json_decode($response->getBody()->getContents());
        } catch(Exception $e) {
            log_message('error', 'Erro ao criar assinatura no Asaas: ' . $e->getMessage());
            return (object)['errors' => [(object)['description' => $e->getMessage()]]];
        }
    }
    
    /* Métodos para processamento de webhooks */
    
    private function process_asaas_payment_confirmation($webhook_data)
    {
        if(!isset($webhook_data->payment->id, $webhook_data->payment->externalReference)) {
            return;
        }
        
        $payment_id = $webhook_data->payment->id;
        $order_id = $webhook_data->payment->externalReference;
        
        // Verificar se é um pedido (formato numérico) ou uma assinatura (formato texto)
        if(is_numeric($order_id)) {
            // Atualizar pedido para pago
            $this->common_m->update(['status' => 2, 'is_payment' => 1, 'payment_by' => 'asaas'], $order_id, 'order_user_list');
            
            // Verificar se pagamento já está registrado
            $payment_exists = $this->common_m->check_payment_exists($payment_id);
            
            if(!$payment_exists) {
                // Obter dados do pedido
                $order_data = $this->common_m->get_order_by_id($order_id);
                
                if($order_data) {
                    // Salvar informações de pagamento
                    $payment_data = [
                        'order_id' => $order_id,
                        'shop_id' => $order_data['shop_id'],
                        'user_id' => $order_data['user_id'],
                        'payment_id' => $payment_id,
                        'amount' => $webhook_data->payment->value,
                        'payment_type' => 'asaas',
                        'status' => 1,
                        'created_at' => d_time(),
                    ];
                    
                    $this->common_m->insert($payment_data, 'order_payment_info');
                }
            }
        }
    }
    
    private function process_asaas_payment_overdue($webhook_data)
    {
        if(!isset($webhook_data->payment->id, $webhook_data->payment->externalReference)) {
            return;
        }
        
        $payment_id = $webhook_data->payment->id;
        $order_id = $webhook_data->payment->externalReference;
        
        // Se for um pedido, marcar como pendente
        if(is_numeric($order_id)) {
            $this->common_m->update(['status' => 0], $order_id, 'order_user_list'); // 0 = pending
        }
    }
    
    private function process_asaas_subscription_renewal($webhook_data)
    {
        if(!isset($webhook_data->payment->subscription)) {
            return;
        }
        
        $subscription_id = $webhook_data->payment->subscription;
        
        // Obter informações da assinatura no banco
        $subscription_info = $this->common_m->get_subscription_by_txn_id($subscription_id);
        
        if($subscription_info) {
            // Atualizar data de expiração
            $package_info = $this->admin_m->get_package_info_by_id($subscription_info['account_type']);
            
            if($package_info) {
                $new_expiry = add_year($package_info['package_type'], $package_info['duration'], $package_info['duration_period'], $subscription_info['expire_date']);
                
                // Atualizar assinatura
                $this->common_m->update([
                    'expire_date' => $new_expiry,
                    'is_running' => 1,
                    'status' => 'active'
                ], $subscription_info['id'], 'payment_info');
                
                // Atualizar usuário
                $this->common_m->update([
                    'end_date' => $new_expiry,
                    'is_expired' => 0
                ], $subscription_info['user_id'], 'users');
            }
        }
    }
    
    private function process_asaas_subscription_overdue($webhook_data)
    {
        if(!isset($webhook_data->payment->subscription)) {
            return;
        }
        
        $subscription_id = $webhook_data->payment->subscription;
        
        // Obter informações da assinatura no banco
        $subscription_info = $this->common_m->get_subscription_by_txn_id($subscription_id);
        
        if($subscription_info) {
            // Marcar assinatura como pendente
            $this->common_m->update([
                'status' => 'pending'
            ], $subscription_info['id'], 'payment_info');
        }
    }
    
    private function process_asaas_subscription_canceled($webhook_data)
    {
        if(!isset($webhook_data->subscription->id)) {
            return;
        }
        
        $subscription_id = $webhook_data->subscription->id;
        
        // Obter informações da assinatura no banco
        $subscription_info = $this->common_m->get_subscription_by_txn_id($subscription_id);
        
        if($subscription_info) {
            // Marcar assinatura como cancelada
            $this->common_m->update([
                'is_running' => 0,
                'status' => 'canceled'
            ], $subscription_info['id'], 'payment_info');
            
            // Marcar usuário como expirado após término da assinatura
            $this->common_m->update([
                'is_expired' => 1
            ], $subscription_info['user_id'], 'users');
        }
    }

    /**
     * Gera QR Code PIX para pagamento via Asaas
     */
    public function generate_asaas_pix()
    {
        $order_id = $this->input->post('order_id', true);
        $amount = floatval($this->input->post('amount'));

        // Obter dados do cliente do formulário
        $customer_data = [
            'name' => $this->input->post('customer_name'),
            'email' => $this->input->post('customer_email'),
            'phone' => $this->input->post('customer_phone'),
            'cpf' => $this->input->post('customer_cpf')
        ];

        // Verificar se temos valores válidos
        if (empty($order_id)) {
            $this->output->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'ID do pedido não informado'
                ]));
            return;
        }

        // Log para debug dos dados recebidos
        error_log('[Asaas PIX] Customer data: ' . json_encode($customer_data));
        error_log('[Asaas PIX] POST data: ' . json_encode($this->input->post()));

        // Validar dados obrigatórios do cliente com verificação mais robusta
        $customer_name = !empty($customer_data['name']) ? trim($customer_data['name']) : '';
        $customer_phone = !empty($customer_data['phone']) ? trim($customer_data['phone']) : '';

        if (empty($customer_name) || empty($customer_phone)) {
            error_log('[Asaas PIX] Validação falhou - Nome: "' . $customer_name . '", Telefone: "' . $customer_phone . '"');
            $this->output->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Nome e telefone são obrigatórios',
                    'debug' => [
                        'customer_name' => $customer_name,
                        'customer_phone' => $customer_phone,
                        'received_data' => $customer_data
                    ]
                ]));
            return;
        }
        
        if ($amount <= 0) {
            // Tentar recuperar o valor do pedido do banco de dados
            $amount = $this->ensure_valid_order_amount($order_id);
            
            // Se ainda for zero ou inválido, retornar erro
            if ($amount <= 0) {
                $this->output->set_content_type('application/json')
                    ->set_output(json_encode([
                        'success' => false,
                        'message' => 'Valor do pedido inválido'
                    ]));
                return;
            }
        }

        // Adicionar log de diagnóstico
        error_log("[Asaas PIX] Gerando PIX para pedido {$order_id} no valor de {$amount}");
        
        // Log para debug
        error_log('[Asaas PIX] Iniciando geração de PIX');
        
        // Obter dados do POST
        $post = $this->input->post();
        error_log('[Asaas PIX] Dados recebidos: ' . json_encode($post));
        
        try {
            // Verificar se temos os dados necessários
            if(empty($post['order_id'])) {
                error_log('[Asaas PIX] Erro: ID do pedido não informado');
                echo json_encode(['status' => 'error', 'message' => 'ID do pedido não informado']);
                return;
            }

            // NOVA VERIFICAÇÃO: Validar o valor do pagamento
            if(empty($post['amount']) || !is_numeric($post['amount']) || floatval($post['amount']) <= 0) {
                error_log('[Asaas PIX] Erro: Valor de pagamento inválido: ' . ($post['amount'] ?? 'vazio'));
                echo json_encode(['status' => 'error', 'message' => 'Valor de pagamento inválido. O valor deve ser maior que zero.']);
                return;
            }

        // VERIFICAÇÃO ESPECÍFICA: Valor mínimo para diferentes tipos de pagamento
        $amount_value = floatval($post['amount']);
        $payment_type = isset($post['payment_type']) ? $post['payment_type'] : 'PIX';

        if($payment_type === 'CREDIT_CARD' && $amount_value < 5.00) {
            error_log('[Asaas Payment] Erro: Valor mínimo para cartão de crédito: R$ 5,00. Valor informado: R$ ' . $amount_value);
            echo json_encode([
                'status' => 'error',
                'message' => 'O valor mínimo para pagamentos com cartão de crédito é R$ 5,00. Valor atual: R$ ' . number_format($amount_value, 2, ',', '.')
            ]);
            return;
        }

        if($payment_type === 'PIX' && $amount_value < 1.00) {
            error_log('[Asaas PIX] Erro: Valor mínimo para PIX: R$ 1,00. Valor informado: R$ ' . $amount_value);
            echo json_encode([
                'status' => 'error',
                'message' => 'O valor mínimo para pagamentos PIX é R$ 1,00. Valor atual: R$ ' . number_format($amount_value, 2, ',', '.')
            ]);
            return;
        }
            
            // Obter configurações do Asaas
            $settings = settings();
            $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
            
            if(empty($asaas) || empty($asaas->asaas_api_key)) {
                error_log('[Asaas PIX] Erro: Configuração do Asaas incompleta');
                echo json_encode(['status' => 'error', 'message' => 'Configuração do Asaas incompleta']);
                return;
            }
            
            $apiKey = $asaas->asaas_api_key;
            $sandbox = !(isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1);
            
            // Carregar nossa classe cliente
            require_once FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';
            
            // Inicializar o cliente Asaas
            $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);
            
            if (!$asaasClient->isReady()) {
                error_log('[Asaas PIX] Erro: Cliente Asaas não inicializado: ' . $asaasClient->getLastError());
                echo json_encode(['status' => 'error', 'message' => 'Erro ao inicializar conexão com Asaas']);
                return;
            }
            
            // Obter informações do usuário
            $user_id = $this->session->userdata('user_id');
            $is_anonymous = empty($user_id);
            
            error_log('[Asaas PIX] User ID da sessão: ' . ($user_id ? $user_id : 'não encontrado'));
            
            if($is_anonymous) {
                $user_id = $this->session->userdata('temp_user_id');
                if(empty($user_id)) {
                    $user_id = rand(10000, 99999);
                    $this->session->set_userdata('temp_user_id', $user_id);
                }
                error_log('[Asaas PIX] Usando ID temporário: ' . $user_id);
            }
            
            // Obter dados do usuário
            $user_info = !$is_anonymous ? $this->common_m->get_user_info_by_id($user_id) : [];
            
            // Garantir que temos informações mínimas do usuário
            if(empty($user_info)) {
                $user_info = [
                    'user_id' => $user_id,
                    'name' => 'Cliente Temporário',
                    'email' => 'cliente'.$user_id.'@temp.com',
                    'phone' => '11999999999',
                    'cpf' => '12345678909' // CPF fictício para testes
                ];
                error_log('[Asaas PIX] Usando dados de usuário fictícios');
            } else {
                error_log('[Asaas PIX] Dados do usuário encontrados no banco');
            }
            
            // Verificar se cliente existe no Asaas
            $customer_id = $this->get_asaas_customer_id($user_id);
                
            // Se não existir, criar um novo
                if(!$customer_id) {
                // Preparar dados do cliente com validações aprimoradas
                // Priorizar dados do formulário sobre dados do banco
                $unique_suffix = time() . rand(100, 999);
                $customer_name = !empty($customer_data['name']) ? $customer_data['name'] :
                                (!empty($user_info['name']) ? $user_info['name'] : 'Cliente ' . $user_id);
                $customer_email = !empty($customer_data['email']) ? $customer_data['email'] :
                                 (!empty($user_info['email']) ? $user_info['email'] : 'cliente'.$unique_suffix.'@temp.com');
                $customer_phone = !empty($customer_data['phone']) ? preg_replace('/[^0-9]/', '', $customer_data['phone']) :
                                 (!empty($user_info['phone']) ? preg_replace('/[^0-9]/', '', $user_info['phone']) : '11999999999');
                $customer_cpf = !empty($customer_data['cpf']) ? preg_replace('/[^0-9]/', '', $customer_data['cpf']) :
                               (!empty($user_info['cpf']) ? preg_replace('/[^0-9]/', '', $user_info['cpf']) : '12345678909');

                // Validações específicas
                if (strlen($customer_phone) < 10) {
                    $customer_phone = '11999999999';
                }

                if (strlen($customer_cpf) != 11) {
                    $customer_cpf = '12345678909';
                }

                $customerData = [
                    'name' => $customer_name,
                    'email' => $customer_email,
                    'phone' => $customer_phone,
                    'cpfCnpj' => $customer_cpf,
                    'externalReference' => 'user_' . $user_id . '_' . $unique_suffix
                ];

                error_log('[Asaas PIX] Criando cliente no Asaas: ' . json_encode($customerData));

                $customer = $asaasClient->createCustomer($customerData);

                if(!$customer || !isset($customer->id)) {
                    $error_msg = $asaasClient->getLastError();
                    error_log('[Asaas PIX] Erro ao criar cliente: ' . $error_msg);

                    // Tentar com CPF alternativo se o erro for relacionado ao CPF
                    if (strpos($error_msg, 'cpfCnpj') !== false || strpos($error_msg, 'CPF') !== false) {
                        error_log('[Asaas PIX] Tentando com CPF alternativo...');
                        $customerData['cpfCnpj'] = '11122233344';
                        $customer = $asaasClient->createCustomer($customerData);

                        if(!$customer || !isset($customer->id)) {
                            // Tentar sem CPF (apenas para sandbox)
                            unset($customerData['cpfCnpj']);
                            $customer = $asaasClient->createCustomer($customerData);
                        }
                    }

                    // Se ainda falhou, retornar erro detalhado
                    if(!$customer || !isset($customer->id)) {
                        $final_error = $asaasClient->getLastError();
                        error_log('[Asaas PIX] Falha final na criação do cliente: ' . $final_error);
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'Erro ao criar cliente no Asaas: ' . $final_error,
                            'debug' => [
                                'customer_data' => $customerData,
                                'original_error' => $error_msg,
                                'final_error' => $final_error
                            ]
                        ]);
                        return;
                    }
                }
                
                $customer_id = $customer->id;
                
                // Salvar o customer_id para uso futuro
                $this->update_asaas_customer_id($user_id, $customer_id);
                
                error_log('[Asaas PIX] Cliente criado com sucesso: ' . $customer_id);
            } else {
                error_log('[Asaas PIX] Cliente existente: ' . $customer_id);
            }
            
            // Calcular data de vencimento para PIX
            $prazo_pix = isset($asaas->asaas_prazo_pix) ? $asaas->asaas_prazo_pix : 1;
            $due_date = date('Y-m-d', strtotime("+{$prazo_pix} days"));
            
            // Processar pagamento com PIX
            $pix_data = [
                'customer' => $customer_id,
                'billingType' => 'PIX',
                'value' => floatval($post['amount']),
                'dueDate' => $due_date,
                'description' => 'Pagamento via PIX - ' . $post['order_id'],
                'externalReference' => $post['order_id']
            ];
            
            error_log('[Asaas PIX] Dados para criação do PIX: ' . json_encode($pix_data));
            
            // Criar pagamento
            $payment = $asaasClient->createPayment($pix_data);
            
            if(!$payment || !isset($payment->id)) {
                $error_msg = $asaasClient->getLastError();
                error_log('[Asaas PIX] Erro ao criar pagamento: ' . $error_msg);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Erro ao criar pagamento PIX: ' . $error_msg
                ]);
                return;
            }
            
                    error_log('[Asaas PIX] Pagamento criado com sucesso: ' . $payment->id);
                    
                    // Obter QR Code do PIX
            $pix_info = $asaasClient->getPixQrCode($payment->id);
            
            if(!$pix_info || !isset($pix_info->payload)) {
                $error_msg = $asaasClient->getLastError();
                error_log('[Asaas PIX] Erro ao obter QR Code PIX: ' . $error_msg);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Erro ao gerar QR Code PIX: ' . $error_msg
                ]);
                return;
            }
            
                            error_log('[Asaas PIX] QR Code gerado com sucesso');
                            
                            // Salvar informações do pagamento na sessão
                            $this->session->set_userdata('asaas_payment_id', $payment->id);
                            $this->session->set_userdata('asaas_order_id', $post['order_id']);
                            
                            echo json_encode([
                                'status' => 'success',
                                'payment_id' => $payment->id,
                                'qrcode' => 'data:image/png;base64,' . $pix_info->encodedImage,
                'qrcode_copy_paste' => $pix_info->payload
            ]);
            
        } catch (Exception $e) {
            error_log('[Asaas PIX] Exceção geral: ' . $e->getMessage());
            error_log('[Asaas PIX] Stack trace: ' . $e->getTraceAsString());
            echo json_encode([
                'status' => 'error',
                'message' => 'Erro ao processar pagamento: ' . $e->getMessage()
            ]);
            return;
        }
    }
    
    /* Método removido para evitar duplicidade */

    public function test_asaas_connection()
    {
        // Desativar a verificação CSRF para esta chamada
        $this->security->csrf_verify = false;
        
        // Exibir todos os erros para diagnóstico
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        
        // Obter configuração do Asaas
        $settings = settings();
        $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
        $apiKey = isset($asaas->asaas_api_key) ? $asaas->asaas_api_key : '';
        $baseUrl = isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1 ? 'https://www.asaas.com/api/v3' : 'https://sandbox.asaas.com/api/v3';
        
        echo "<h1>Teste de Conexão Asaas</h1>";
        echo "<p>Ambiente: " . (isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1 ? 'Produção' : 'Sandbox') . "</p>";
        echo "<p>Base URL: $baseUrl</p>";
        echo "<p>API Key: " . (empty($apiKey) ? 'Não configurada' : substr($apiKey, 0, 5) . '...') . "</p>";

        // Testar conexão com o Asaas
        try {
            $client = new GuzzleHttp\Client();
            $response = $client->request('GET', $baseUrl . '/customers', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'access_token' => $apiKey
                ]
            ]);
            
            $statusCode = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents());
            
            echo "<p style='color:green;'>Conexão bem-sucedida! Status: $statusCode</p>";
            echo "<p>Dados recebidos: " . json_encode($body) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color:red;'>Erro na conexão: " . $e->getMessage() . "</p>";
            
            // Mostrar informações detalhadas do erro
            if ($e instanceof GuzzleHttp\Exception\RequestException) {
                if ($e->hasResponse()) {
                    $response = $e->getResponse();
                    echo "<p>Status do erro: " . $response->getStatusCode() . "</p>";
                    echo "<p>Resposta: " . $response->getBody() . "</p>";
                }
            }
        }
    }

    public function set_temp_user_id()
    {
        $this->security->csrf_verify = false;
        $temp_user_id = $this->input->post('temp_user_id');
        if ($temp_user_id) {
            $this->session->set_userdata('temp_user_id', $temp_user_id);
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error']);
        }
    }

    public function get_asaas_customer_id($user_id)
    {
        if(empty($user_id)) {
            error_log('[Asaas] Erro ao obter ID do cliente: user_id vazio');
            return null;
        }
        
        error_log('[Asaas] Buscando customer_id para user_id: ' . $user_id);
        
        try {
            // Verificar primeiro na tabela users
            $user_result = $this->db->select('asaas_customer_id')
                               ->where('id', $user_id)
                               ->get('users')
                               ->row();
                               
            if($user_result && !empty($user_result->asaas_customer_id)) {
                error_log('[Asaas] Encontrado customer_id na tabela users: ' . $user_result->asaas_customer_id);
                return $user_result->asaas_customer_id;
            }
            
            // Verificar na tabela específica se existir
            if($this->db->table_exists('asaas_customers')) {
                $customer_result = $this->db->select('customer_id')
                                         ->where('user_id', $user_id)
                                         ->get('asaas_customers')
                                         ->row();
                                         
                if($customer_result && !empty($customer_result->customer_id)) {
                    error_log('[Asaas] Encontrado customer_id na tabela asaas_customers: ' . $customer_result->customer_id);
                    
                    // Atualizar também na tabela users para manter consistência
                    $this->update_asaas_customer_id($user_id, $customer_result->customer_id);
                    
                    return $customer_result->customer_id;
                }
            }
            
            error_log('[Asaas] Nenhum customer_id encontrado para user_id: ' . $user_id);
            return null;
        } catch(Exception $e) {
            error_log('[Asaas] Erro ao obter ID do cliente: ' . $e->getMessage());
            return null;
        }
    }
    
    public function update_asaas_customer_id($user_id, $customer_id)
    {
        if(empty($user_id) || empty($customer_id)) {
            error_log('[Asaas] Erro ao atualizar ID do cliente: user_id ou customer_id vazios');
            return false;
        }
        
        error_log('[Asaas] Atualizando customer_id para user_id: ' . $user_id . ' -> ' . $customer_id);
        
        try {
            // Verificar se a coluna existe na tabela users
            $fields = $this->db->list_fields('users');
            
            if(in_array('asaas_customer_id', $fields)) {
                // Atualizar na tabela users
                $result = $this->db->where('id', $user_id)
                                   ->update('users', ['asaas_customer_id' => $customer_id]);
                
                error_log('[Asaas] Atualização na tabela users: ' . ($result ? 'sucesso' : 'falha'));
                
                // Se falhou, tentar criar a coluna
                if(!$result) {
                    error_log('[Asaas] Tentando criar a coluna asaas_customer_id na tabela users');
                    $this->db->query("ALTER TABLE users ADD COLUMN asaas_customer_id VARCHAR(100) NULL");
                    
                    // Tentar atualizar novamente
                    $result = $this->db->where('id', $user_id)
                                       ->update('users', ['asaas_customer_id' => $customer_id]);
                    
                    error_log('[Asaas] Atualização após criar coluna: ' . ($result ? 'sucesso' : 'falha'));
                }
            } else {
                // Tentar criar a coluna
                error_log('[Asaas] Coluna asaas_customer_id não existe na tabela users. Tentando criar...');
                $this->db->query("ALTER TABLE users ADD COLUMN asaas_customer_id VARCHAR(100) NULL");
                
                // Tentar atualizar
                $result = $this->db->where('id', $user_id)
                                   ->update('users', ['asaas_customer_id' => $customer_id]);
                
                error_log('[Asaas] Atualização após criar coluna: ' . ($result ? 'sucesso' : 'falha'));
            }
            
            // Verificar se também existe uma tabela específica para dados do Asaas
            if($this->db->table_exists('asaas_customers')) {
                // Verificar se o registro já existe
                $existing = $this->db->where('user_id', $user_id)
                                    ->get('asaas_customers')
                                    ->row();
                
                if($existing) {
                    // Atualizar registro existente
                    $result = $this->db->where('user_id', $user_id)
                                       ->update('asaas_customers', ['customer_id' => $customer_id]);
                } else {
                    // Inserir novo registro
                    $result = $this->db->insert('asaas_customers', [
                        'user_id' => $user_id,
                        'customer_id' => $customer_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                
                error_log('[Asaas] Atualização na tabela asaas_customers: ' . ($result ? 'sucesso' : 'falha'));
            }
            
            return true;
        } catch(Exception $e) {
            error_log('[Asaas] Erro ao atualizar ID do cliente: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Método para lidar com o problema de URLs com "undefinedpayment"
     * Este método captura todas as chamadas com padrão "undefinedpayment/endpoint"
     * e redireciona para o endpoint correto
     */
    public function undefinedpayment($endpoint = null)
    {
        // Logar o problema para diagnóstico
        error_log("Correção de rota: undefinedpayment/$endpoint redirecionado para payment/$endpoint");
        
        // Verificar se temos um endpoint válido
        if ($endpoint && method_exists($this, $endpoint)) {
            // Redirecionar para o método correto
            return $this->$endpoint();
        }
        
        // Caso não tenhamos um endpoint válido, retornar erro
        $this->output->set_status_header(404);
        return $this->output->set_output(json_encode([
            'status' => 'error',
            'message' => 'Endpoint não encontrado'
        ]));
    }

    /**
     * Método de diagnóstico para problemas de pagamento
     * Este método exibe informações detalhadas para ajudar a diagnosticar problemas
     */
    public function diagnose_payment()
    {
        // Método para diagnóstico de problemas de pagamento
        $order_id = $this->input->get('order_id', true);
        
        echo '<h1>Diagnóstico de Pagamento</h1>';
        echo '<h2>Informações do Sistema</h2>';
        echo '<p>Data/Hora: ' . date('Y-m-d H:i:s') . '</p>';
        echo '<p>Versão PHP: ' . phpversion() . '</p>';
        
        if (!empty($order_id)) {
            echo '<h2>Dados do Pedido</h2>';
            
            // Carregar modelo necessário
            $this->load->model('common_m');
            $order_info = $this->common_m->get_by_id($order_id, 'order_user_list');
            
            if (!empty($order_info)) {
                echo '<table border="1" cellpadding="5" cellspacing="0">';
                echo '<tr><th>Campo</th><th>Valor</th></tr>';
                echo '<tr><td>ID</td><td>' . $order_info['id'] . '</td></tr>';
                echo '<tr><td>Referência</td><td>' . ($order_info['uid'] ?? 'N/A') . '</td></tr>';
                echo '<tr><td>Total</td><td>' . $order_info['total'] . '</td></tr>';
                echo '<tr><td>Status</td><td>' . $order_info['status'] . '</td></tr>';
                echo '<tr><td>Data de Criação</td><td>' . $order_info['created_at'] . '</td></tr>';
                echo '</table>';
                
                // Verificar se o valor é válido
                $total = floatval($order_info['total']);
                if ($total <= 0) {
                    echo '<div style="background-color: #ffcccc; padding: 10px; margin-top: 10px; border: 1px solid #ff0000;">';
                    echo '<strong>ERRO:</strong> O valor do pedido é zero ou inválido (' . $total . '). Isso impedirá o processamento do pagamento.';
                    echo '</div>';
                } else {
                    echo '<div style="background-color: #ccffcc; padding: 10px; margin-top: 10px; border: 1px solid #00ff00;">';
                    echo '<strong>OK:</strong> O valor do pedido parece válido (' . $total . ').';
                    echo '</div>';
                }
            } else {
                echo '<div style="background-color: #ffcccc; padding: 10px; margin-top: 10px; border: 1px solid #ff0000;">';
                echo '<strong>ERRO:</strong> Pedido não encontrado com ID ' . $order_id;
                echo '</div>';
            }
        } else {
            echo '<h2>Como usar</h2>';
            echo '<p>Adicione o parâmetro <code>order_id</code> na URL para diagnosticar um pedido específico.</p>';
            echo '<p>Exemplo: <code>' . base_url('payment/diagnose_payment?order_id=123') . '</code></p>';
        }
        
        // Testar URLs para verificar prefixo undefined
        echo '<h2>Teste de URLs</h2>';
        echo '<p>Base URL: ' . base_url() . '</p>';
        echo '<p>URL de Processamento: ' . base_url('payment/process_asaas_payment') . '</p>';
        echo '<p>URL de PIX: ' . base_url('payment/generate_asaas_pix') . '</p>';
        
        // Verificar se há o problema conhecido com "undefinedpayment"
        if (strpos(base_url('payment/process_asaas_payment'), 'undefinedpayment') !== false) {
            echo '<div style="background-color: #ffcccc; padding: 10px; margin-top: 10px; border: 1px solid #ff0000;">';
            echo '<strong>ERRO:</strong> Detectado o problema "undefinedpayment" nas URLs. Isso pode causar falha nas requisições.';
            echo '</div>';
        }
        
        echo '<h2>Parâmetros da Requisição</h2>';
        echo '<pre>';
        print_r($_GET);
        echo '</pre>';
        
        echo '<h2>Sessão Atual</h2>';
        echo '<pre>';
        print_r($this->session->userdata());
        echo '</pre>';
    }

    /**
     * Process PIX payment via Asaas
     */
    public function process_asaas_pix()
    {
        // Set content type for JSON response
        header('Content-Type: application/json');

        // Load required dependencies
        $this->load->helper('asaas');
        $this->load->model('common_m');

        // Basic security check
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Método não permitido']);
            return;
        }

        $order_id = $this->input->post('order_id', TRUE);
        $amount = floatval($this->input->post('amount', TRUE));
        $customer_name = $this->input->post('customer_name', TRUE);
        $customer_email = $this->input->post('customer_email', TRUE);
        $customer_cpf = $this->input->post('customer_cpf', TRUE);
        $customer_phone = $this->input->post('customer_phone', TRUE);

        // Basic validation
        if (empty($order_id) || $amount <= 0 || empty($customer_name) || empty($customer_email) || empty($customer_cpf)) {
            echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não informados']);
            return;
        }

        try {
            // Get Asaas configuration
            $settings = settings();
            if (!isset($settings['asaas_config']) || empty($settings['asaas_config'])) {
                echo json_encode(['success' => false, 'message' => 'Configuração do Asaas não encontrada']);
                return;
            }

            $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config'], true) : null;
            if (!$asaas || !isset($asaas['asaas_api_key'])) {
                echo json_encode(['success' => false, 'message' => 'Chave API do Asaas não configurada']);
                return;
            }

            $apiKey = $asaas['asaas_api_key'];
            $sandbox = !(isset($asaas['is_asaas_live']) && $asaas['is_asaas_live'] == 1);

            if (empty($apiKey)) {
                echo json_encode(['success' => false, 'message' => 'Chave API do Asaas está vazia']);
                return;
            }

            // Initialize Asaas client with fallback
            $asaasClient = null;
            $client_file = FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';
            $fallback_file = FCPATH . 'asaas_scripts/AsaasCurlClient.php';

            if (file_exists($client_file)) {
                require_once $client_file;
                try {
                    $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);
                } catch (Exception $e) {
                    error_log('[Asaas PIX] GuzzleClient failed: ' . $e->getMessage());
                    $asaasClient = null;
                }
            }

            // Fallback to cURL client if GuzzleHttp fails
            if (!$asaasClient && file_exists($fallback_file)) {
                require_once $fallback_file;
                $asaasClient = new AsaasCurlClient($apiKey, $sandbox);
            }

            if (!$asaasClient || !$asaasClient->isReady()) {
                $error = $asaasClient ? $asaasClient->getLastError() : 'Cliente Asaas não disponível';
                echo json_encode(['success' => false, 'message' => 'Erro ao conectar com Asaas: ' . $error]);
                return;
            }

            // Create customer data following working test format
            $phone_clean = preg_replace('/\D/', '', $customer_phone);

            // Use working phone format from tests
            $landline_phone = '4832830001'; // Working landline from tests
            $mobile_phone = '48999990001';  // Working mobile from tests

            // If user provided phone, try to use it but ensure it's valid
            if (strlen($phone_clean) >= 10) {
                if (strlen($phone_clean) == 10) {
                    // Convert 10 digits to 11 by adding 9
                    $mobile_phone = substr($phone_clean, 0, 2) . '9' . substr($phone_clean, 2);
                    $landline_phone = $phone_clean;
                } else {
                    $mobile_phone = $phone_clean;
                    $landline_phone = substr($phone_clean, 0, 10); // Remove extra digit for landline
                }
            }

            // Follow exact structure from working tests
            $customer_data = [
                'name' => $customer_name,
                'email' => $customer_email,
                'cpfCnpj' => preg_replace('/\D/', '', $customer_cpf),
                'phone' => $landline_phone,        // Both phone and mobilePhone like in tests
                'mobilePhone' => $mobile_phone,
                'externalReference' => 'pix_' . $order_id . '_' . time()
            ];

            $customer = $asaasClient->createCustomer($customer_data);
            if (!$customer) {
                $error_msg = $asaasClient->getLastError();

                // Log detailed error for debugging
                error_log('[PIX Payment] Customer creation failed. Data sent: ' . json_encode($customer_data));
                error_log('[PIX Payment] API Error: ' . $error_msg);

                // Try to extract meaningful error from Asaas API response
                if (empty($error_msg)) {
                    $error_msg = 'Erro desconhecido na API do Asaas';
                } elseif (strpos($error_msg, 'invalid_phone') !== false) {
                    $error_msg = 'Número de telefone inválido. Use o formato: (11) 99999-9999';
                } elseif (strpos($error_msg, 'invalid_mobilePhone') !== false) {
                    $error_msg = 'Número de celular inválido. Use o formato: (11) 99999-9999';
                } elseif (strpos($error_msg, 'invalid_email') !== false) {
                    $error_msg = 'E-mail inválido. Verifique o formato do e-mail.';
                } elseif (strpos($error_msg, 'invalid_cpfCnpj') !== false) {
                    $error_msg = 'CPF inválido. Verifique os números digitados.';
                }

                echo json_encode(['success' => false, 'message' => 'Erro ao criar cliente: ' . $error_msg]);
                return;
            }

            // Ensure customer ID is accessible (handle both array and object returns)
            $customer_id = is_array($customer) ? $customer['id'] : $customer->id;

            // Create PIX payment
            $payment_data = [
                'customer' => $customer_id,
                'billingType' => 'PIX',
                'value' => $amount,
                'dueDate' => date('Y-m-d', strtotime('+1 day')),
                'description' => 'Pedido #' . $order_id,
                'externalReference' => $order_id
            ];

            $payment = $asaasClient->createPayment($payment_data);
            if (!$payment) {
                $error_msg = $asaasClient->getLastError();
                error_log('[PIX Payment] Erro ao criar pagamento: ' . $error_msg);
                echo json_encode(['success' => false, 'message' => 'Erro ao criar pagamento PIX: ' . $error_msg]);
                return;
            }
            error_log('[PIX Payment] Pagamento criado: ' . json_encode($payment));

            // Handle both array and object returns for payment
            $payment_id = is_array($payment) ? $payment['id'] : $payment->id;
            $payment_status = is_array($payment) ? $payment['status'] : $payment->status;
            $payment_due_date = is_array($payment) ? $payment['dueDate'] : $payment->dueDate;

            // Get PIX QR Code
            $pixData = $asaasClient->getPixQrCode($payment_id);
            if (!$pixData) {
                $error_msg = $asaasClient->getLastError();
                error_log('[PIX Payment] Failed to get QR Code for payment: ' . $payment_id . '. Error: ' . $error_msg);
                echo json_encode(['success' => false, 'message' => 'Erro ao gerar QR Code PIX: ' . $error_msg]);
                return;
            }
            error_log('[PIX Payment] Dados do QR Code recebidos: ' . json_encode($pixData));

            // Convert PIX data to array for consistent access
            $pixData_array = is_array($pixData) ? $pixData : (array)$pixData;

            // Verify required fields exist
            if (!isset($pixData_array['encodedImage']) || !isset($pixData_array['payload'])) {
                error_log('[PIX Payment] Missing required fields in PIX data: ' . json_encode($pixData_array));
                echo json_encode(['success' => false, 'message' => 'Dados do QR Code PIX incompletos']);
                return;
            }

            $qr_image = $pixData_array['encodedImage'];
            $qr_payload = $pixData_array['payload'];

            // Verify data is not empty
            if (empty($qr_image) || empty($qr_payload)) {
                error_log('[PIX Payment] Empty QR Code data. Image length: ' . strlen($qr_image) . ', Payload length: ' . strlen($qr_payload));
                echo json_encode(['success' => false, 'message' => 'QR Code PIX vazio ou inválido']);
                return;
            }

            echo json_encode([
                'success' => true,
                'status' => 'success', // Frontend expects this
                'payment_id' => $payment_id,
                'qrcode' => 'data:image/png;base64,' . $qr_image, // Frontend expects this format
                'qrcode_copy_paste' => $qr_payload, // Frontend expects this name
                'expiration_formatted' => date('d/m/Y H:i', strtotime($payment_due_date))
            ]);

        } catch (Error $e) {
            error_log('[Process Asaas PIX] Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            echo json_encode(['success' => false, 'message' => 'Erro fatal do servidor', 'debug' => $e->getMessage()]);
        } catch (Exception $e) {
            error_log('[Process Asaas PIX] Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            echo json_encode(['success' => false, 'message' => 'Erro interno do servidor', 'debug' => $e->getMessage()]);
        }
    }

    /**
     * Process Credit Card payment via Asaas
     */
    public function process_asaas_credit_card()
    {
        // Set content type for JSON response
        header('Content-Type: application/json');

        // Load required dependencies
        $this->load->helper('asaas');
        $this->load->model('common_m');

        // Add error handling for missing dependencies
        if (!function_exists('settings')) {
            echo json_encode(['success' => false, 'message' => 'Função settings não encontrada']);
            return;
        }

        // Basic security check
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Método não permitido']);
            return;
        }

        $order_id = $this->input->post('order_id', TRUE);
        $amount = floatval($this->input->post('amount', TRUE));
        $holderName = $this->input->post('holderName', TRUE);
        $email = $this->input->post('email', TRUE);
        $holderCpf = $this->input->post('holderCpf', TRUE);
        $holderPhone = $this->input->post('holderPhone', TRUE);
        $address = $this->input->post('address', TRUE);
        $address_number = $this->input->post('address_number', TRUE);
        $postal_code = $this->input->post('postal_code', TRUE);
        $cardNumber = $this->input->post('cardNumber', TRUE);
        $ccv = $this->input->post('ccv', TRUE);
        $cardHolderName = $this->input->post('cardHolderName', TRUE);
        $expiry = $this->input->post('expiry', TRUE);
        $installments = intval($this->input->post('installments', TRUE)) ?: 1;

        // Basic validation
        if (empty($order_id) || $amount <= 0 || empty($holderName) || empty($email) || empty($cardNumber)) {
            echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não informados']);
            return;
        }

        try {
            // Log start of processing
            error_log('[Credit Card Payment] Starting payment processing for order: ' . $order_id);

            // Get Asaas configuration
            $settings = settings();
            error_log('[Credit Card Payment] Settings loaded successfully');
            $asaas = isJson($settings['asaas_config']) ? json_decode($settings['asaas_config']) : '';
            $apiKey = isset($asaas->asaas_api_key) ? $asaas->asaas_api_key : '';
            $sandbox = !(isset($asaas->is_asaas_live) && $asaas->is_asaas_live == 1);

            if (empty($apiKey)) {
                echo json_encode(['success' => false, 'message' => 'Configuração do Asaas não encontrada']);
                return;
            }

            // Initialize Asaas client
            error_log('[Credit Card Payment] Loading AsaasGuzzleClient');
            require_once FCPATH . 'asaas_scripts/AsaasGuzzleClient.php';
            $asaasClient = new AsaasGuzzleClient($apiKey, $sandbox);
            error_log('[Credit Card Payment] AsaasGuzzleClient initialized');

            if (!$asaasClient->isReady()) {
                echo json_encode(['success' => false, 'message' => 'Erro ao conectar com Asaas: ' . $asaasClient->getLastError()]);
                return;
            }

            // Create customer data following working test format
            $phone_clean = preg_replace('/\D/', '', $holderPhone);

            // Use working phone format from tests
            $landline_phone = '4832830001'; // Working landline from tests
            $mobile_phone = '48999990001';  // Working mobile from tests

            // If user provided phone, try to use it but ensure it's valid
            if (strlen($phone_clean) >= 10) {
                if (strlen($phone_clean) == 10) {
                    // Convert 10 digits to 11 by adding 9
                    $mobile_phone = substr($phone_clean, 0, 2) . '9' . substr($phone_clean, 2);
                    $landline_phone = $phone_clean;
                } else {
                    $mobile_phone = $phone_clean;
                    $landline_phone = substr($phone_clean, 0, 10); // Remove extra digit for landline
                }
            }

            // Follow exact structure from working tests - minimal required fields only
            $customer_data = [
                'name' => $holderName,
                'email' => $email,
                'cpfCnpj' => preg_replace('/\D/', '', $holderCpf),
                'phone' => $landline_phone,        // Both phone and mobilePhone like in tests
                'mobilePhone' => $mobile_phone,
                'externalReference' => 'cc_' . $order_id . '_' . time()
            ];

            $customer = $asaasClient->createCustomer($customer_data);
            if (!$customer) {
                $error_msg = $asaasClient->getLastError();

                // Log detailed error for debugging
                error_log('[Credit Card Payment] Customer creation failed. Data sent: ' . json_encode($customer_data));
                error_log('[Credit Card Payment] API Error: ' . $error_msg);

                // Try to extract meaningful error from Asaas API response
                if (empty($error_msg)) {
                    $error_msg = 'Erro desconhecido na API do Asaas';
                } elseif (strpos($error_msg, 'invalid_phone') !== false) {
                    $error_msg = 'Número de telefone inválido. Use o formato: (11) 99999-9999';
                } elseif (strpos($error_msg, 'invalid_mobilePhone') !== false) {
                    $error_msg = 'Número de celular inválido. Use o formato: (11) 99999-9999';
                } elseif (strpos($error_msg, 'invalid_email') !== false) {
                    $error_msg = 'E-mail inválido. Verifique o formato do e-mail.';
                } elseif (strpos($error_msg, 'invalid_cpfCnpj') !== false) {
                    $error_msg = 'CPF inválido. Verifique os números digitados.';
                }

                echo json_encode(['success' => false, 'message' => 'Erro ao criar cliente: ' . $error_msg]);
                return;
            }

            // Prepare card data
            $expiry_parts = explode('/', $expiry);
            $card_data = [
                'holderName' => $cardHolderName,
                'number' => preg_replace('/\D/', '', $cardNumber),
                'expiryMonth' => $expiry_parts[0],
                'expiryYear' => '20' . $expiry_parts[1],
                'ccv' => $ccv
            ];

            // Convert customer object to array if needed
            $customer_array = is_array($customer) ? $customer : (array)$customer;

            // Calculate installment value if needed
            $installment_value = $amount;
            if ($installments > 1) {
                $installment_value = round($amount / $installments, 2);
            }

            // Create credit card payment
            $payment_data = [
                'customer' => $customer_array['id'],
                'billingType' => 'CREDIT_CARD',
                'value' => $amount,
                'dueDate' => date('Y-m-d'),
                'description' => 'Pedido #' . $order_id,
                'externalReference' => $order_id,
                'installmentCount' => $installments,
                'installmentValue' => $installment_value, // Required for installments
                'creditCard' => $card_data,
                'creditCardHolderInfo' => [
                    'name' => $holderName,
                    'email' => $email,
                    'cpfCnpj' => preg_replace('/\D/', '', $holderCpf),
                    'postalCode' => preg_replace('/\D/', '', $postal_code),
                    'addressNumber' => $address_number,
                    'phone' => $landline_phone // Use same phone format as customer
                ]
            ];

            // Log payment data for debugging
            error_log('[Credit Card Payment] Payment data: ' . json_encode($payment_data));

            $payment = $asaasClient->createPayment($payment_data);
            if (!$payment) {
                $error_msg = $asaasClient->getLastError();

                // Log detailed error for debugging
                error_log('[Credit Card Payment] Payment creation failed. Error: ' . $error_msg);

                // Try to extract meaningful error from Asaas API response
                if (strpos($error_msg, 'valor da parcela') !== false || strpos($error_msg, 'installment') !== false) {
                    $error_msg = 'Erro no cálculo das parcelas. Tente novamente ou escolha pagamento à vista.';
                } elseif (strpos($error_msg, 'invalid_creditCard') !== false) {
                    if (strpos($error_msg, 'expirado') !== false) {
                        $error_msg = 'Cartão de crédito expirado. Verifique a data de validade.';
                    } else {
                        $error_msg = 'Dados do cartão de crédito inválidos. Verifique número, CVV e validade.';
                    }
                } elseif (strpos($error_msg, 'insufficient_funds') !== false) {
                    $error_msg = 'Cartão sem limite suficiente para esta transação.';
                } elseif (strpos($error_msg, 'card_declined') !== false) {
                    $error_msg = 'Cartão recusado pelo banco emissor.';
                }
                echo json_encode(['success' => false, 'message' => 'Erro ao processar pagamento: ' . $error_msg]);
                return;
            }

            // Convert payment object to array if needed
            $payment_array = is_array($payment) ? $payment : (array)$payment;

            if ($payment_array['status'] === 'CONFIRMED') {
                // Payment confirmed immediately
                $this->common_m->update_by_uid(['is_payment' => 1, 'payment_by' => 'asaas_credit_card'], $order_id);

                echo json_encode([
                    'success' => true,
                    'status' => 'success', // Frontend expects this
                    'redirect_url' => base_url('payment/asaas_success?payment_id=' . $payment_array['id'])
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Pagamento não foi aprovado. Status: ' . $payment_array['status']]);
            }

        } catch (Error $e) {
            error_log('[Process Asaas Credit Card] Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            echo json_encode(['success' => false, 'message' => 'Erro fatal do servidor', 'debug' => $e->getMessage()]);
        } catch (Exception $e) {
            error_log('[Process Asaas Credit Card] Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            echo json_encode(['success' => false, 'message' => 'Erro interno do servidor', 'debug' => $e->getMessage()]);
        }
    }

    /**
     * Processa pagamento PIX via Mercado Pago
     */
    public function process_mercado_pix()
    {
        $this->security->csrf_verify = false;
        
        // Validar dados de entrada
        $required_fields = ['customer_name', 'customer_email', 'customer_cpf', 'order_id', 'amount'];
        foreach ($required_fields as $field) {
            if (empty($this->input->post($field))) {
                echo json_encode(['status' => 'error', 'message' => "Campo {$field} é obrigatório"]);
                return;
            }
        }
        
        try {
            // Obter configurações do restaurante
            $payment_data = $this->session->userdata('payment');
            $shop_id = isset($payment_data['shop_id']) ? $payment_data['shop_id'] : 0;
            $restaurant = $this->common_m->get_restaurant_info_by_id($shop_id);
            
            // Verificar se deve usar configuração existente ou específica
            $settings = $restaurant;
            $use_existing = isset($settings['mercado_pix_use_existing']) && $settings['mercado_pix_use_existing'] == 1;
            
            if ($use_existing && !empty($settings['mercado_config'])) {
                $mercado_config = json_decode($settings['mercado_config'], true);
            } else {
                $mercado_config = [
                    'mercado_pix_access_token' => $settings['mercado_pix_access_token'] ?? '',
                    'mercado_pix_public_key' => $settings['mercado_pix_public_key'] ?? ''
                ];
            }
            
            if (empty($mercado_config['mercado_pix_access_token'])) {
                throw new Exception('Token de acesso do Mercado Pago não configurado');
            }
            
            // Configurar SDK
            MercadoPago\SDK::setAccessToken($mercado_config['mercado_pix_access_token']);
            
            // Dados do pagamento
            $payment_data = [
                "transaction_amount" => floatval($this->input->post('amount')),
                "description" => "Pedido #" . $this->input->post('order_id'),
                "payment_method_id" => "pix",
                "payer" => [
                    "email" => $this->input->post('customer_email'),
                    "first_name" => $this->input->post('customer_name'),
                    "identification" => [
                        "type" => "CPF",
                        "number" => preg_replace('/[^0-9]/', '', $this->input->post('customer_cpf'))
                    ]
                ]
            ];
            
            // Criar pagamento PIX
            $payment = new MercadoPago\Payment();
            foreach ($payment_data as $key => $value) {
                $payment->$key = $value;
            }
            
            $payment->save();
            error_log("Mercado Pago PIX - Resposta do save(): " . json_encode($payment));
            
            if ($payment->status == 'pending') {
                $response = [
                    'status' => 'success',
                    'payment_id' => $payment->id,
                    'qr_code' => $payment->point_of_interaction->transaction_data->qr_code,
                    'qr_code_base64' => $payment->point_of_interaction->transaction_data->qr_code_base64,
                    'expiration_date' => $payment->date_of_expiration
                ];
                
                echo json_encode($response);
            } else {
                error_log("Mercado Pago PIX - Erro ao gerar PIX: " . $payment->status_detail);
                throw new Exception('Erro ao gerar PIX: ' . $payment->status_detail);
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error', 
                'message' => 'Erro ao processar pagamento: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Webhook para receber notificações do Mercado Pago PIX
     */
    public function mercado_pix_webhook()
    {
        $this->security->csrf_verify = false;
        
        try {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            // Log da notificação recebida
            error_log("Mercado PIX Webhook: " . print_r($data, true));
            
            if (isset($data['type']) && $data['type'] == 'payment') {
                $payment_id = $data['data']['id'];
                
                // Buscar configuração do Mercado Pago PIX
                // Por enquanto, usar configuração global - pode ser melhorado para identificar o restaurante
                $settings = settings();
                $mercado_pix_config = !empty($settings['mercado_pix_config']) ? json_decode($settings['mercado_pix_config'], true) : [];
                
                if (!empty($mercado_pix_config['mercado_pix_access_token'])) {
                    MercadoPago\SDK::setAccessToken($mercado_pix_config['mercado_pix_access_token']);
                    
                    $payment = MercadoPago\Payment::find_by_id($payment_id);
                    
                    if ($payment->status == 'approved') {
                        // Processar aprovação do pagamento
                        $this->_process_mercado_pix_approval($payment);
                    }
                }
            }
            
            http_response_code(200);
            echo "OK";
            
        } catch (Exception $e) {
            error_log("Webhook Mercado PIX error: " . $e->getMessage());
            http_response_code(500);
        }
    }

    /**
     * Processa a aprovação do pagamento PIX do Mercado Pago
     */
    private function _process_mercado_pix_approval($payment)
    {
        try {
            // Lógica para atualizar pedido como pago
            // Similar ao que já existe para outros gateways
            $data = [
                'amount' => $payment->transaction_amount,
                'currency' => $payment->currency_id,
                'status' => 'success',
                'txn_id' => $payment->id,
                'payment_type' => 'mercado_pix',
                'all_info' => json_encode($payment)
            ];
            
            // Identificar o slug e account_slug do pedido
            // Esta lógica pode precisar ser ajustada conforme a estrutura do sistema
            $description = $payment->description;
            if (preg_match('/Pedido #(.+)/', $description, $matches)) {
                $order_id = $matches[1];
                
                // Buscar informações do pedido para identificar o restaurante
                $order_info = $this->common_m->get_by_id($order_id, 'order_user_list');
                if (!empty($order_info)) {
                    $shop_info = $this->common_m->get_by_id($order_info['shop_id'], 'restaurant_list');
                    if (!empty($shop_info)) {
                        // Processar sucesso do pagamento
                        $this->send_success($shop_info['username'], '', $data);
                    }
                }
            }
            
        } catch (Exception $e) {
            error_log("Error processing Mercado PIX approval: " . $e->getMessage());
        }
    }

    /**
     * Verifica o status do pagamento PIX do Mercado Pago usando cURL direto
     */
    public function check_mercado_pix_status()
    {
        $this->security->csrf_verify = false;
        header('Content-Type: application/json');
        
        try {
            $payment_id = $this->input->post('payment_id');
            
            if (empty($payment_id)) {
                echo json_encode(['status' => 'error', 'message' => 'ID do pagamento não informado']);
                return;
            }
            
            // Obter configurações do restaurante
            $payment_data = $this->session->userdata('payment');
            $shop_id = isset($payment_data['shop_id']) ? $payment_data['shop_id'] : 0;
            
            if (empty($shop_id)) {
                throw new Exception('ID do restaurante não encontrado na sessão');
            }
            
            $restaurant = $this->common_m->get_restaurant_info_by_id($shop_id);
            
            if (empty($restaurant)) {
                throw new Exception('Restaurante não encontrado');
            }
            
            // Determinar qual configuração usar
            $access_token = '';
            $use_existing = isset($restaurant['mercado_pix_use_existing']) && $restaurant['mercado_pix_use_existing'] == 1;
            
            if ($use_existing && !empty($restaurant['mercado_config'])) {
                // Usar configuração existente do Mercado Pago
                $mercado_config = json_decode($restaurant['mercado_config'], true);
                $access_token = isset($mercado_config['mercado_access_token']) ? $mercado_config['mercado_access_token'] : '';
            } else {
                // Usar configuração específica do PIX
                $mercado_pix_config = !empty($restaurant['mercado_pix_config']) ? 
                    json_decode($restaurant['mercado_pix_config'], true) : [];
                $access_token = isset($mercado_pix_config['mercado_pix_access_token']) ? 
                    $mercado_pix_config['mercado_pix_access_token'] : '';
            }
            
            if (empty($access_token)) {
                throw new Exception('Token de acesso não configurado');
            }
            
            // URL da API para verificar status
            $url = "https://api.mercadopago.com/v1/payments/{$payment_id}";
            
            // Headers para requisição
            $headers = [
                'Authorization: Bearer ' . $access_token,
                'Content-Type: application/json'
            ];
            
            // Configurar cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            
            // Executar requisição
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                throw new Exception("Erro de conexão: " . $curl_error);
            }
            
            if ($http_code === 200) {
                $payment_info = json_decode($response, true);
                
                echo json_encode([
                    'status' => $payment_info['status'],
                    'status_detail' => isset($payment_info['status_detail']) ? $payment_info['status_detail'] : ''
                ]);
            } else {
                throw new Exception("Erro ao consultar pagamento (HTTP {$http_code})");
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error', 
                'message' => 'Erro ao verificar status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Gera pagamento PIX do Mercado Pago usando cURL direto (contorna limitações do SDK 2.4.3)
     */
    public function generate_mercado_pix()
    {
        $this->security->csrf_verify = false;
        
        // Headers para resposta JSON
        header('Content-Type: application/json');
        
        try {
            // Validar dados de entrada
            $required_fields = ['customer_name', 'customer_email', 'customer_cpf', 'order_id', 'amount'];
            foreach ($required_fields as $field) {
                if (empty($this->input->post($field))) {
                    echo json_encode(['status' => 'error', 'message' => "Campo {$field} é obrigatório"]);
                    return;
                }
            }
            
            // Obter configurações do restaurante
            $payment_data = $this->session->userdata('payment');
            $shop_id = isset($payment_data['shop_id']) ? $payment_data['shop_id'] : 0;
            
            if (empty($shop_id)) {
                throw new Exception('ID do restaurante não encontrado na sessão');
            }
            
            $restaurant = $this->common_m->get_restaurant_info_by_id($shop_id);
            
            if (empty($restaurant)) {
                throw new Exception('Restaurante não encontrado');
            }
            
            // Verificar se PIX está ativo
            if (empty($restaurant['is_mercado_pix']) || $restaurant['mercado_pix_status'] != 1) {
                throw new Exception('PIX Mercado Pago não está ativo para este restaurante');
            }
            
            // Determinar qual configuração usar
            $access_token = '';
            $use_existing = isset($restaurant['mercado_pix_use_existing']) && $restaurant['mercado_pix_use_existing'] == 1;
            
            if ($use_existing && !empty($restaurant['mercado_config'])) {
                // Usar configuração existente do Mercado Pago
                $mercado_config = json_decode($restaurant['mercado_config'], true);
                $access_token = isset($mercado_config['mercado_access_token']) ? $mercado_config['mercado_access_token'] : '';
            } else {
                // Usar configuração específica do PIX
                $mercado_pix_config = !empty($restaurant['mercado_pix_config']) ? 
                    json_decode($restaurant['mercado_pix_config'], true) : [];
                $access_token = isset($mercado_pix_config['mercado_pix_access_token']) ? 
                    $mercado_pix_config['mercado_pix_access_token'] : '';
            }
            
            if (empty($access_token)) {
                throw new Exception('Token de acesso do Mercado Pago não configurado');
            }
            
            // Preparar dados do PIX
            $amount = floatval($this->input->post('amount'));
            $customer_cpf = preg_replace('/[^0-9]/', '', $this->input->post('customer_cpf'));
            $order_id = $this->input->post('order_id');
            $customer_name = $this->input->post('customer_name');
            $customer_email = $this->input->post('customer_email');
            
            // Log para debug
            error_log("PIX Debug - Access Token configurado: " . (!empty($access_token) ? "SIM" : "NÃO"));
            error_log("PIX Debug - Use existing: " . ($use_existing ? "SIM" : "NÃO"));
            error_log("PIX Debug - Valor: " . $amount);
            
            // Dados do pagamento PIX para API
            $payment_data = [
                "transaction_amount" => $amount,
                "description" => "Pedido #" . $order_id . " - " . $restaurant['name'],
                "payment_method_id" => "pix",
                "payer" => [
                    "email" => $customer_email,
                    "first_name" => $customer_name,
                    "identification" => [
                        "type" => "CPF",
                        "number" => $customer_cpf
                    ]
                ]
            ];
            
            // Headers obrigatórios para API
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $access_token,
                'X-Idempotency-Key: ' . uniqid('pix_order_' . $order_id . '_', true)
            ];
            
            // URL da API do Mercado Pago
            $url = 'https://api.mercadopago.com/v1/payments';
            
            // Configurar cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            // Executar requisição
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($curl_error) {
                throw new Exception("Erro de conexão: " . $curl_error);
            }
            
            // Processar resposta da API
            $payment_response = json_decode($response, true);
            
            // Log da resposta para debug
            error_log("PIX Debug - HTTP Code: " . $http_code);
            error_log("PIX Debug - Response: " . $response);
            
            if ($http_code === 201 && isset($payment_response['id'])) {
                // Sucesso - PIX criado
                if ($payment_response['status'] === 'pending' && 
                    isset($payment_response['point_of_interaction']['transaction_data']['qr_code'])) {
                    
                    $qr_code = $payment_response['point_of_interaction']['transaction_data']['qr_code'];
                    $qr_code_base64 = isset($payment_response['point_of_interaction']['transaction_data']['qr_code_base64']) ? 
                                      $payment_response['point_of_interaction']['transaction_data']['qr_code_base64'] : '';
                    
                    $response_data = [
                        'status' => 'success',
                        'payment_id' => $payment_response['id'],
                        'qr_code' => $qr_code,
                        'qr_code_base64' => $qr_code_base64,
                        'amount' => $amount,
                        'expiration_date' => isset($payment_response['date_of_expiration']) ? $payment_response['date_of_expiration'] : null
                    ];
                    
                    echo json_encode($response_data);
                    
                } else {
                    throw new Exception('Erro: PIX não foi gerado corretamente. Status: ' . $payment_response['status']);
                }
                
            } else {
                // Erro na API
                $error_message = "Erro na API do Mercado Pago";
                
                if (isset($payment_response['message'])) {
                    $error_message = $payment_response['message'];
                } elseif (isset($payment_response['cause'])) {
                    $causes = array_map(function($cause) {
                        return $cause['code'] . ': ' . $cause['description'];
                    }, $payment_response['cause']);
                    $error_message = implode('; ', $causes);
                }
                
                throw new Exception($error_message);
            }
            
        } catch (Exception $e) {
            error_log("PIX Error: " . $e->getMessage());
            echo json_encode([
                'status' => 'error', 
                'message' => 'Erro ao processar pagamento: ' . $e->getMessage()
            ]);
        }
    }
}
