<?php
/**
 * Asaas Payment Method Registration Check and Fix
 * This script ensures <PERSON><PERSON><PERSON> is properly registered in the payment system
 */

echo "🔍 Asaas Payment Method Registration Check\n";
echo "==========================================\n\n";

// Try to load database configuration from CodeIgniter
$db_config = [
    'hostname' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'cardapio_delivery',
    'charset' => 'utf8'
];

echo "⚠️  Using default database configuration\n";
echo "💡 If this fails, please update the database configuration in this script\n";
echo "\n🔌 Connecting to database...\n";
echo "Host: {$db_config['hostname']}\n";
echo "Database: {$db_config['database']}\n";
echo "Username: {$db_config['username']}\n\n";

try {
    $pdo = new PDO(
        "mysql:host={$db_config['hostname']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "✅ Database connection established\n\n";
        
        // Check if payment_method_list table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'payment_method_list'");
        if ($stmt->rowCount() == 0) {
            echo "❌ payment_method_list table not found!\n";
            exit;
        }
        
        echo "✅ payment_method_list table found\n";
        
        // Check current Asaas registration
        $stmt = $pdo->prepare("SELECT * FROM payment_method_list WHERE slug = 'asaas'");
        $stmt->execute();
        $asaas_method = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($asaas_method) {
            echo "✅ Asaas payment method found:\n";
            echo "   ID: {$asaas_method['id']}\n";
            echo "   Name: {$asaas_method['name']}\n";
            echo "   Slug: {$asaas_method['slug']}\n";
            echo "   Active Slug: {$asaas_method['active_slug']}\n";
            echo "   Status Slug: {$asaas_method['status_slug']}\n";
            echo "   Status: {$asaas_method['status']}\n\n";
            
            // Check if the registration is correct
            $expected = [
                'name' => 'Asaas',
                'slug' => 'asaas',
                'active_slug' => 'asaas_status',
                'status_slug' => 'is_asaas',
                'status' => 1
            ];
            
            $needs_update = false;
            foreach ($expected as $field => $value) {
                if ($asaas_method[$field] != $value) {
                    echo "⚠️  Field '{$field}' needs update: '{$asaas_method[$field]}' -> '{$value}'\n";
                    $needs_update = true;
                }
            }
            
            if ($needs_update) {
                echo "\n🔧 Updating Asaas payment method registration...\n";
                $stmt = $pdo->prepare("UPDATE payment_method_list SET name = ?, active_slug = ?, status_slug = ?, status = ? WHERE slug = 'asaas'");
                $stmt->execute([$expected['name'], $expected['active_slug'], $expected['status_slug'], $expected['status']]);
                echo "✅ Asaas payment method updated successfully!\n";
            } else {
                echo "✅ Asaas payment method registration is correct!\n";
            }
            
        } else {
            echo "❌ Asaas payment method not found. Creating new entry...\n";
            
            $stmt = $pdo->prepare("INSERT INTO payment_method_list (name, slug, active_slug, status_slug, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['Asaas', 'asaas', 'asaas_status', 'is_asaas', 1]);
            
            echo "✅ Asaas payment method created successfully!\n";
            echo "   Insert ID: " . $pdo->lastInsertId() . "\n";
        }
        
        // Check if restaurant_list table has required columns
        echo "\n🔍 Checking restaurant_list table for Asaas columns...\n";
        
        $stmt = $pdo->query("DESCRIBE restaurant_list");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = ['is_asaas', 'asaas_status', 'asaas_config'];
        $missing_columns = [];
        
        foreach ($required_columns as $column) {
            if (!in_array($column, $columns)) {
                $missing_columns[] = $column;
            }
        }
        
        if (!empty($missing_columns)) {
            echo "⚠️  Missing columns in restaurant_list: " . implode(', ', $missing_columns) . "\n";
            echo "🔧 Adding missing columns...\n";
            
            foreach ($missing_columns as $column) {
                switch ($column) {
                    case 'is_asaas':
                        $pdo->exec("ALTER TABLE restaurant_list ADD COLUMN is_asaas INT(11) DEFAULT 0");
                        echo "✅ Added column: is_asaas\n";
                        break;
                    case 'asaas_status':
                        $pdo->exec("ALTER TABLE restaurant_list ADD COLUMN asaas_status INT(11) DEFAULT 0");
                        echo "✅ Added column: asaas_status\n";
                        break;
                    case 'asaas_config':
                        $pdo->exec("ALTER TABLE restaurant_list ADD COLUMN asaas_config LONGTEXT NULL");
                        echo "✅ Added column: asaas_config\n";
                        break;
                }
            }
        } else {
            echo "✅ All required columns exist in restaurant_list table\n";
        }
        
        // Check settings table for global Asaas configuration
        echo "\n🔍 Checking settings table for global Asaas configuration...\n";
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT * FROM settings WHERE type = 'asaas_status' OR type = 'is_asaas' OR type = 'asaas_config'");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($settings)) {
                echo "⚠️  No Asaas settings found in settings table\n";
                echo "🔧 Adding default Asaas settings...\n";
                
                $default_settings = [
                    ['type' => 'is_asaas', 'value' => '0'],
                    ['type' => 'asaas_status', 'value' => '0'],
                    ['type' => 'asaas_config', 'value' => '{}']
                ];
                
                foreach ($default_settings as $setting) {
                    $stmt = $pdo->prepare("INSERT INTO settings (type, value) VALUES (?, ?) ON DUPLICATE KEY UPDATE value = VALUES(value)");
                    $stmt->execute([$setting['type'], $setting['value']]);
                    echo "✅ Added/updated setting: {$setting['type']}\n";
                }
            } else {
                echo "✅ Asaas settings found in settings table:\n";
                foreach ($settings as $setting) {
                    echo "   {$setting['type']}: {$setting['value']}\n";
                }
            }
        } else {
            echo "⚠️  Settings table not found\n";
        }
        
        echo "\n🎉 Asaas registration check completed!\n";
        echo "\nNext steps:\n";
        echo "1. Configure Asaas API credentials in admin panel\n";
        echo "2. Enable Asaas payment method for restaurants\n";
        echo "3. Test payment flows\n";
        
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
